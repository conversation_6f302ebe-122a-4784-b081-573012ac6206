import * as vscode from 'vscode';
import {
  addRouter,
  getBaseUrl,
  getJdhCgiUrl,
  getVscodeConfig,
  GlobalState,
  isIDE,
  setVscodeConfig,
} from '@joycoder/shared';
import CompletionCodeLensProvider from '@joycoder/plugin-base-code-completion/src/utils/codeLensProcider';
import { JoyCoderProvider } from '@joycoder/agent-driven/src/core/webview/JoycoderProvider';
import { DEFAULT_AUTO_APPROVAL_SETTINGS } from '@joycoder/agent-driven/src/shared/AutoApprovalSettings';
import { isRemoteEnvironment } from '@joycoder/agent-driven/src/utils/fs';
import { modes } from '@joycoder/agent-driven/web-agent/src/utils/modes';
import { getSettingWebview } from '.';
import { DataSetsAPI } from '../knowledgeBase/api';

export async function setConfigInfo(data, isLeft) {
  switch (data.type) {
    case 'genTask':
      setVscodeConfig('JoyCode.config.codeCompletionsGenTask', data.genTask);
      break;
    case 'completionDelay':
      GlobalState.update('JoyCode.completion.delay', data.completionDelay);
      break;
    case 'maxConcurrentFileReads':
      GlobalState.update('maxConcurrentFileReads', data.maxConcurrentFileReads);
      break;
    case 'completionsMoreContext':
      setVscodeConfig('JoyCode.config.codeCompletionsMoreContext', data.completionsMoreContext);
      break;
    case 'commitCodeReview':
      setVscodeConfig('JoyCode.config.codeReview.commit', data.commitCodeReview);
      break;
    case 'silentMode':
      GlobalState.update('isSilentMode', data.silentMode);
      break;
    case 'errorLine':
      setVscodeConfig('JoyCode.config.codeReview.errorLine', data.errorLine);
      break;
    case 'isShenYi':
      setVscodeConfig('JoyCode.config.shenYi.commit', data.isShenYi);
      break;
    case 'commitMessage':
      setVscodeConfig('JoyCode.config.codeReview.commitMessageStyle', data.commitMessage);
      break;
    case 'codeLens':
      GlobalState.update('JoyCode.codeLens.menuList', data.codeLens);
      const codeLensProvider = CompletionCodeLensProvider.vsCodeLensInstance;
      codeLensProvider?.disposeCodeLenses?.();
      codeLensProvider?.rerenderCodeLenses?.();
      break;
    case 'moreSetting':
      vscode.commands.executeCommand('JoyCode.openConfigPage');
      break;
    case 'chatgpt-setting':
      vscode.commands.executeCommand('JoyCode.config.setting', { isLeft });
      break;
    case 'writeAction':
      GlobalState.update('diffEnabled', data.writeAction === 'diff');
      GlobalState.update('JoyCode.autoCode.writeAction', data.writeAction);
      break;
    case 'autoApprovalSettings':
      GlobalState.update('autoApprovalSettings', data.autoApprovalSettings);
      vscode.commands.executeCommand('joycoder.autoCode.syncState');
      try {
        await setSettingWebviewData(undefined, { type: 'agent' });
      } catch (error) {
        console.error('Backend: setSettingWebviewData 调用失败', error);
      }
      break;
    case 'autoExecute':
      GlobalState.update('autoExecute', data.autoExecute);
      vscode.commands.executeCommand('joycoder.autoCode.syncState');
      try {
        await setSettingWebviewData(undefined, { type: 'agent' });
      } catch (error) {
        console.error('Backend: setSettingWebviewData 调用失败', error);
      }
      break;
    case 'customInstructions':
      GlobalState.update('customInstructions', data.customInstructions);
      vscode.commands.executeCommand('joycoder.autoCode.syncState');
      break;
    case 'isTodoListEnableChange':
      setVscodeConfig('JoyCode.config.todoListEnabled', data.isTodoListEnable);
      break;
    case 'createProjectRule':
      try {
        // 获取工作区路径
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
          throw new Error('未找到有效的工作区');
        }

        // 使用工作区URI来构建路径
        const ruleDir = vscode.Uri.joinPath(workspaceFolder.uri, '.joycode', 'rules');
        const ruleFile = vscode.Uri.joinPath(ruleDir, data.filePath.split('/').pop() || '');

        // 确保目录存在
        try {
          await vscode.workspace.fs.createDirectory(ruleDir);
        } catch (err) {
          // 如果创建目录失败，尝试在用户目录下创建
          const userHomeDir = vscode.Uri.joinPath(workspaceFolder.uri, '.joycoder-user', 'rules');
          await vscode.workspace.fs.createDirectory(userHomeDir);
          const ruleFile = vscode.Uri.joinPath(userHomeDir, data.filePath.split('/').pop() || '');
          await vscode.workspace.fs.writeFile(ruleFile, Buffer.from(data.ruleContent, 'utf-8'));
          vscode.window.showInformationMessage(`规则文件已创建在用户目录: ${ruleFile.fsPath}`);
          const document = await vscode.workspace.openTextDocument(ruleFile);
          await vscode.window.showTextDocument(document);
          break;
        }

        // 检查文件是否已存在
        try {
          await vscode.workspace.fs.stat(ruleFile);
          // 如果文件存在，显示提醒并打开现有文件
          vscode.window.showWarningMessage(`规则文件 ${data.filePath} 已存在，可直接在此文件内修改。`);
        } catch {
          // 如果文件不存在，创建新文件
          await vscode.workspace.fs.writeFile(ruleFile, Buffer.from(data.ruleContent, 'utf-8'));
          vscode.window.showInformationMessage(`规则文件 ${data.filePath} 创建成功`);
        }

        // 打开文件（无论是新创建的还是已存在的）
        const document = await vscode.workspace.openTextDocument(ruleFile);
        await vscode.window.showTextDocument(document);
      } catch (error) {
        vscode.window.showErrorMessage(`操作规则文件失败: ${error.message}`);
      }
      break;
    case 'chatgpt-setting-error':
      vscode.window.showErrorMessage(data.message || '发生未知错误');
      break;
    default:
      break;
  }
}

export const setSettingWebviewData = async (
  settingWebview?: vscode.WebviewPanel,
  data?: { isLeft?: boolean; type: string; mcpSubtype?: string }
) => {
  settingWebview = settingWebview ? settingWebview : getSettingWebview();
  if (!settingWebview) return;
  const userInfo = GlobalState.get('jdhLoginInfo');
  const completionDelay = GlobalState.get('JoyCode.completion.delay') || 1.2;
  const genTask = getVscodeConfig('JoyCode.config.codeCompletionsGenTask') ?? 'LINE';
  const completionsMoreContext = getVscodeConfig('JoyCode.config.codeCompletionsMoreContext') ?? false;
  const commitCodeReview = getVscodeConfig('JoyCode.config.codeReview.commit') ?? true; // 增量评审
  const silentMode = GlobalState.get('isSilentMode') ?? true; // 后台编辑
  const isShenYi = getVscodeConfig('JoyCode.config.shenYi.commit') ?? false; // 增量神医
  const codeReviewImmediate = getVscodeConfig('JoyCode.config.codeReview.immediate') ?? false; // 主动评审
  const errorLine = getVscodeConfig('JoyCode.config.codeReview.errorLine') ?? false; // 错误行
  const commitMessage = getVscodeConfig('JoyCode.config.codeReview.commitMessageStyle') ?? 'GIT_SCHEMA'; // 错误行
  if (!GlobalState.get('JoyCode.codeLens.menuList')) {
    GlobalState.update('JoyCode.codeLens.menuList', [
      'functionComment',
      'codeReview',
      'reconstruction',
      'comment',
      'test',
    ]);
  }
  const writeAction = GlobalState.get('JoyCode.autoCode.writeAction') || 'all';
  const codeLens = GlobalState.get('JoyCode.codeLens.menuList');
  const enableCodeLens = getVscodeConfig('JoyCode.config.codeLens-row-menus');
  const autoApprovalSettings = GlobalState.get('autoApprovalSettings') || DEFAULT_AUTO_APPROVAL_SETTINGS;
  const autoExecute = GlobalState.get('autoExecute') ?? false;
  const customInstructions = GlobalState.get('customInstructions');
  const enableCodebase = getVscodeConfig('JoyCode.enableCodebase');
  const hasWorkspaceFolder = !!vscode.workspace.workspaceFolders;
  const isTodoListEnable = getVscodeConfig('JoyCode.config.todoListEnabled', true); // 默认启用
  const browserToolEnabled = GlobalState.get('browserToolEnabled') ?? false; // 浏览器是否打开
  const browserViewportSize = GlobalState.get('browserSettings') || {};
  const remoteBrowserEnabled = GlobalState.get('remoteBrowserEnabled') ?? false;
  const remoteBrowserHost = GlobalState.get('remoteBrowserHost') ?? undefined;

  const showCodebase = enableCodebase;

  // 获取多模式信息
  let modesInfo = {
    defaultModes: [] as any[],
    customModes: [] as any[],
    currentMode: 'chat',
  };

  try {
    const joyCoderProvider = JoyCoderProvider.getVisibleInstance();
    if (joyCoderProvider) {
      const state = await joyCoderProvider.getState();

      // 从 agent-driven 获取默认模式
      modesInfo = {
        defaultModes: Array.from(modes) || [],
        customModes: state.customModes || [],
        currentMode: state.mode || 'chat',
      };
    }
  } catch (error) {
    console.warn('获取多模式信息失败:', error);
  }

  settingWebview?.webview.postMessage({
    type: 'COMMON',
    payload: {
      type: 'switch-setting-view',
      data: {
        type: 'setting',
        completionDelay,
        genTask,
        completionsMoreContext,
        commitCodeReview,
        silentMode,
        writeAction,
        isShenYi,
        codeReviewImmediate,
        errorLine,
        commitMessage,
        codeLens,
        enableCodeLens,
        autoApprovalSettings,
        autoExecute,
        customInstructions,
        showCodebase,
        hasWorkspaceFolder,
        userInfo,
        modesInfo, // 添加多模式信息
        isTodoListEnable, // 添加todolist启用状态
        cmdData: data || null,
        isRemoteEnvironment: isRemoteEnvironment(),
        isIDE: isIDE(),
        baseUrl: getJdhCgiUrl(getBaseUrl()),
        browserToolEnabled,
        browserViewportSize,
        remoteBrowserEnabled,
        remoteBrowserHost,
      },
      isLeft: data?.isLeft === undefined || !!data?.isLeft,
    },
  });
};

/**
 * 每隔指定时间执行一次回调函数，持续执行直到手动停止
 * @param callback 可选的回调函数，在每次执行时调用
 * @param interval 间隔时间（毫秒）
 * @returns 返回一个清理函数，可用于停止执行
 */
export async function printPeriodically(
  callback?: (count: number, timestamp: Date) => void | Promise<void>,
  interval = 5000,
  maxCount = 6 // 默认执行6次，即30秒（5秒 * 6）
) {
  let count = 0;
  let timeoutId: NodeJS.Timeout | null = null;
  let isRunning = true;

  const cleanup = () => {
    isRunning = false;
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  const print = async () => {
    if (!isRunning) return;

    count++;
    const timestamp = new Date();

    // 安全调用回调函数
    try {
      await callback?.(count, timestamp);
    } catch (error) {
      console.error('Callback execution failed:', error);
      // 可以选择是否继续执行或停止
      cleanup();
      return; // 如果希望出错时停止
    }

    // 检查是否达到最大执行次数
    if (count >= maxCount) {
      console.log(`[printPeriodically] 已达到最大执行次数 ${maxCount}，停止执行`);
      cleanup();
      return;
    }

    if (!isRunning) return;

    // 使用固定间隔，避免执行时间影响下一次调用
    timeoutId = setTimeout(print, interval);
  };

  // 立即开始第一次执行
  try {
    await print();
  } catch (error) {
    cleanup();
    throw error;
  }

  // 返回清理函数
  return cleanup;
}

export async function getDocList({ page, page_size, datasetId, settingWebview }) {
  const list = await DataSetsAPI.getDocList({
    page,
    page_size,
    dataset_id: datasetId,
  });

  await settingWebview?.webview.postMessage({
    type: 'COMMON',
    payload: {
      type: 'update-doc-info',
      data: {
        list,
        dataType: 'list',
      },
    },
  });
}

export function addJoyspaceRouterIn(settingWebview, callback) {
  const clearTimeId = setTimeout(() => {
    callback({ meCode: null, meExpireIn: null });
  }, 120000);
  addRouter({
    path: '/openDoc',
    callback: async (uri) => {
      vscode.commands.executeCommand('JoyCode.config.setting');
      setSettingWebviewData(settingWebview, { type: 'codebase' });
      const info: RegExpMatchArray | null = uri.query.match(/meCode=([^$&]+)/);
      const meExpireIn: RegExpMatchArray | null = uri.query.match(/meExpireIn=([^$&]+)/);
      if (info && info[1] && meExpireIn?.[1]) {
        try {
          const meCode = info[1];
          callback && callback({ meCode, meExpireIn: meExpireIn[1] });
          clearTimeout(clearTimeId);
        } catch (error) {}
      }
    },
  });
}
