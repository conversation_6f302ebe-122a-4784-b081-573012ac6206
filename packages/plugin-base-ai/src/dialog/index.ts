import * as vscode from 'vscode';
import {
  Logger,
  getLastAcitveTextEditor,
  getVscodeConfig,
  openInVscode,
  openInBrowser,
  isBusiness,
  reportRd,
  GlobalState,
  reportUmp,
  ActionType,
  setVscodeConfig,
  reportAction,
  PLUGIN_ID,
  getSelectedText,
  getSelectionInfo,
  checkLogin,
  eventEmitter,
  isBusinessLocal,
  chatModelConfigsLocal,
  IActionCustomReportParam,
  getFeedbackConfig,
  getRepositoryStatus,
  getJdhLoginInfo,
} from '@joycoder/shared';
import {
  ViewLoader,
  THEME_HTML,
  MARKDOWN_HTML,
  LOADING_HTML,
  ChatGPTWebviewSendEvent,
  ChatGPTWebviewReceiveEvent,
} from '@joycoder/web';
import { debounce, uniqueId } from 'lodash';
import { Scene, ChatGPTAskResponseData, ChatProxyService } from '../proxy/index';
import { askSecGPT } from '@joycoder/plugin-base-sec/src/index';
import AdoptResultCache from '@joycoder/plugin-base-code-completion/src/stats/adoptResultCache';
import { showAIChatView } from '@joycoder/plugin-base-tree-view/src/commands';
import { Message } from '@joycoder/web/src/messages/messageTypes';
import { getRemoteConfigSync, forceJdhLogin } from '@joycoder/shared';
import { needUpdateVsCodeGuide } from '@joycoder/version';
import { UserPromptManager } from '@joycoder/agent-driven/src/core/user-pormpt';
import {
  constant,
  CodeLensMenuChatType,
  AskChatType,
  RightMenuChatType,
  CodeTranslateChatType,
  CHAT_TYPE_TO_REPORT_ACTION_TYPE,
  SCENE_TYPE_TO_REPORT_ACTION_TYPE,
  CodeReviewChatType,
} from './constant';
import getBatchChatHistory, { MessageContent } from '../proxy/messages';
import agentRequest from '../langchain/agent/api';
import { IResponse } from '../langchain/agent';
import { AgentSceneType } from '../langchain/tools/shared/toolType';
import { postPromptListMessageToWebview, setPrompt, unsetPrompt } from './prompt';
import { openHtmlCodeInVscodeWithBrowser } from '@joycoder/plugin-base-browser';
import { getChatModelAndConfig, setChatModel, ChatModel, ChatModelConfig, getChatModel } from '../model';
import path from 'path';
import chatAgentVscode from '@joycoder/agent-common/src/vscode/chatAgentVscode';
import chatD2CVscode from '@joycoder/agent-common/src/vscode/chatD2CVscode';
// import { getJoyCoderContextMenus } from '../contextMenu';
import { codeStatService } from '@joycoder/plugin-base-code-completion/src/stats/codeStatService';
import { addJoyspaceRouterIn, getDocList, printPeriodically, setConfigInfo, setSettingWebviewData } from './setting';
import { CodebaseIndexingStatus, CodebaseProgress } from '@joycoder/shared/src/types/codebase';
import { CodebaseManager } from '@joycoder/shared/src/codebase';
import { mcpExecuteCommand } from './mcpExecuteCommand';
import { JoyCoderProvider } from '@joycoder/agent-driven/src/core/webview/JoycoderProvider';
import { defaultModeSlug } from '@joycoder/agent-driven/web-agent/src/utils/modes';
import { JoyCoderMCPMessageMap } from '@joycoder/agent-driven/src/adaptor/translate/message';
import { openFile } from '@joycoder/agent-driven/src/integrations/misc/open-file';
import { isRemoteEnvironment } from '@joycoder/agent-driven/src/utils/fs';
import { getWorkspacePath } from '@joycoder/agent-driven/src/utils/path';
import { discoverChromeHostUrl, tryChromeHostUrl } from '@joycoder/agent-driven/src/services/browser/browserDiscovery';
import { FileSystemHelper } from '@joycoder/agent-driven/src/utils/FileSystemHelper';
import * as os from 'os';
import { getChatPrompt, saveChatPrompt } from './chatRecord';
import { DataSetsAPI } from '../knowledgeBase/api';

export const WELCOME_QUESTION = '你好';

export let decorationType: vscode.Disposable | vscode.TextEditorDecorationType = new vscode.Disposable(() => {});
let abortController: AbortController | null = null;
let chatGptWebviewLeftLoaded = false;
let chatGptLeftAsked = false;
let chatGptWebviewMessageCache: Message[] = [];
let chatGptWebviewLeft: ViewLoader | null = null;
let lsFnCodelensLoading = false;
let settingWebview: vscode.WebviewPanel | undefined = undefined;
/**
 * 所有GPT配置项，更新到webview中
 *
 */
export async function postConfigMessageToWebview(
  updateConfig?: Partial<{
    model: string;
    knowledge: string;
    theme: string;
    conversation: string;
    context: boolean;
    repoStatus?: boolean;
  }>
) {
  const modelConfig = getChatModelAndConfig(updateConfig?.model);
  const model = modelConfig.label;
  const repoStatus = await getRepositoryStatus();
  const config = {
    model,
    modelConfig,
    repoStatus,
    context: GlobalState.get('chatCodeContext'),
    theme: GlobalState.get('chatCurrentTheme'),
    conversation: GlobalState.get('chatCurrentIsConversation') === true ? '开启' : '关闭',
    ...updateConfig,
  };
  postMessageToWebview({
    type: 'COMMON',
    payload: {
      type: 'updateGPTConfig',
      data: config,
      isLeft: true,
    },
  });
}

export function postMessageToWebview<T extends Message>(params: T) {
  if (chatGptWebviewLeftLoaded) {
    chatGptWebviewLeft?.postMessageToWebview(params);
  } else {
    chatGptWebviewMessageCache.push(params);
  }
}

function postCahceMessageToWebview() {
  if (
    chatGptWebviewMessageCache &&
    chatGptWebviewMessageCache.length &&
    chatGptWebviewLeft &&
    chatGptWebviewLeftLoaded
  ) {
    const messagesToRemove: number[] = [];
    chatGptWebviewMessageCache.forEach((message, index) => {
      if (chatGptWebviewLeftLoaded) {
        chatGptWebviewLeft?.postMessageToWebview(message);
        messagesToRemove.push(index);
      }
    });
    for (let i = messagesToRemove.length - 1; i >= 0; i--) {
      chatGptWebviewMessageCache.splice(messagesToRemove[i], 1);
    }
  }
}

/**
 * 提前结束
 *
 */
function handleEndChatGPT(options?: { isLeft?: boolean; data: any }) {
  abortController?.abort();
  postMessageToWebview({
    type: 'COMMON',
    payload: {
      type: 'addResponseEnd',
      data: { appendResponse: '您提前中止了本次问答~', ...options?.data, selectedFileList: [] },
      isLeft: !!options?.isLeft,
    },
  });
  lsFnCodelensLoading = false;
  GlobalState.update('JoyCode.isCodeReviewing', false);
}

/**
 * 设置ChatGPT
 *
 */
function handleSettingChatGPT(configId?: string) {
  // wq.webmonitor.hibox.chatgpt.setting.all
  reportUmp(36, 0, configId);
  vscode.commands.executeCommand('workbench.action.openSettings', configId || `@ext:${PLUGIN_ID}`);
}
interface IHandleAskOptions {
  type?: AskChatType | ChatGPTWebviewSendEvent;
  scene?: AgentSceneType;
  sessionId?: string;
  imageUrlList?: string[];
  selectedFileList?: any[];
  agentTools?: Array<string | [string, Record<string, any>]>;
  onlineSearch?: boolean; //是否开启联网搜索，输入可能会传入搜索引擎
  systemMessage?: string;
  pageTitle?: string;
  webviewView?: vscode.WebviewView;
  conversationId?: string;
  parentMessageId?: string;
}
/**
 * 处理ChatGPT
 *
 * @export
 * @param {string} prompt
 * @param {string} [code]
 * @return {*}
 */
export async function handleAskChatGPT(
  /**
   * 用户输入
   * */
  prompt: string,
  /**
   * 提取的代码
   * */
  code: string | unknown,
  /**
   * 选项
   * */
  options: IHandleAskOptions
): Promise<undefined | Partial<ChatGPTAskResponseData & IResponse>> {
  // 提前处理（因为异常也需要返回）Assistant消息的模型名和对应模型头像
  const modelConfig = getChatModelAndConfig();
  const modelResult = { model: modelConfig?.label, avatar: modelConfig?.avatar || '' };

  const startTime = Date.now();
  reportUmp(24, 1);

  if (options.webviewView) {
    try {
      options.webviewView?.onDidDispose(() => {
        options = {
          ...options,
          webviewView: undefined,
          pageTitle: undefined,
        };
        chatGptWebviewLeft && chatGptWebviewLeft.dispose();
        chatGptWebviewLeft = null;
        chatGptLeftAsked = false;
        GlobalState.update('JoyCode.isCodeReviewing', false);
        chatGptWebviewLeftLoaded = false;
      });
      !isBusiness && vscode.commands.executeCommand('JoyCode.view.aichat');
      !options.webviewView.visible && options.webviewView.show(true);
    } catch (error) {
      GlobalState.update('JoyCode.isCodeReviewing', false);
      !options.webviewView.visible && (await vscode.commands.executeCommand('JoyCode-left-view.focus'));
    }
  }
  // 3. 创建或打开webview
  const webviewLoader = openInVscode({
    pageName: 'chatGPT',
    pageTitle: options.pageTitle || 'AI助手',
    webviewView: options.webviewView,
    layout: options.webviewView
      ? null
      : vscode.window.activeTextEditor
      ? { orientation: 0, groups: [{ size: 0.7 }, { size: 0.3 }] }
      : { orientation: 0, groups: [{}] },
    // css文件文件走cdn加载，减少插件大小
    htmlPrepend: `${LOADING_HTML}${THEME_HTML}${MARKDOWN_HTML}\n<script>window.joyCoderLeftWebview=${!!options.webviewView}</script>`,

    async onDidReceiveMessage({ type, data = {} }) {
      switch (type) {
        case 'chatgpt-ask':
          const opt = {
            pageTitle: options.pageTitle,
            webviewView: options.webviewView,
          };
          handleAskChatGPT(data.userContent, data.contextCode, {
            ...opt,
            type: data.type,
            scene: data.scene,
            imageUrlList: data.imageUrlList,
            selectedFileList: data.selectedFileList,
            systemMessage: data.systemMessage,
            agentTools: data.agentTools,
            onlineSearch: data.onlineSearch,
            sessionId: data.sessionId,
            conversationId: data.sessionId || data.conversationId,
            parentMessageId: data.parentMessageId,
          });
          break;
        case 'chatgpt-prompt':
          const promptString = `请你帮我优化下提示词，尽可能明确提示词细节来达到更好的生成效果。\n注意！用纯文本返回，不要引号；只返回优化后的提示词，不要执行提示词；不要其他说明信息，不要口语化表达；如果用户要求输出形式或者格式，需要重点注意保留。\n用户提示词如下：\n\n${data.userContent}`;
          const resData = await callLLM(promptString, {
            model: '京东言犀', //模型标签，京东言犀、DeepSeek-coder
          });
          const resPrompt = resData.fullText || '';
          postMessageToWebview({
            type: 'COMMON',
            payload: {
              type: 'addPrompt',
              data: {
                ...resData,
                response: resPrompt,
              },
              isLeft: true,
            },
          });
          return;
          break;
        case 'chatgpt-stop':
          handleEndChatGPT({ isLeft: true, data: { ...modelResult } });
          break;
        case 'chatgpt-retry':
          handleAskChatGPT(data.prompt, data.code, {
            ...options,
            type: data.type,
            scene: data.scene,
            imageUrlList: data.imageUrlList,
            selectedFileList: data.selectedFileList,
            systemMessage: data.systemMessage,
            agentTools: data.agentTools,
            onlineSearch: data.onlineSearch,
            sessionId: data.sessionId,
            conversationId: data.sessionId || data.conversationId,
            parentMessageId: data.parentMessageId,
          });
          break;
        case 'chatgpt-setting':
          handleSettingChatGPT(data);
          break;
        case 'chatgpt-check-update':
          if (isBusiness()) {
            // 商业版不检查更新
            Logger.showInformationMessage('您当前已是最新版本~');
            break;
          }
          vscode.commands.executeCommand('JoyCode.checkUpdate');
          break;
        case 'chatgpt-get-model':
          const chatGPTModelConfigs: ChatModelConfig[] = isBusinessLocal()
            ? chatModelConfigsLocal
            : getRemoteConfigSync().chatGPTModelConfigs ?? [];
          // 过滤掉隐藏的模型，一些历史过期模型逐步淘汰
          const items = chatGPTModelConfigs.filter(
            (modelConfig) =>
              !modelConfig.hidden &&
              (!modelConfig.hasOwnProperty('modelFunctionType') ||
                modelConfig.modelFunctionType === 'ALL' ||
                modelConfig.modelFunctionType === 'chat' ||
                modelConfig.modelFunctionType === 'chat')
          );
          const chatModelConfigsTest = isBusiness() ? '[]' : getVscodeConfig('JoyCode.config.test-llm-list') ?? '[]';
          postMessageToWebview({
            type: 'COMMON',
            payload: {
              type: 'updateGPTModel',
              data: [...items, ...JSON.parse(chatModelConfigsTest || '[]')],
              isLeft: true,
            },
          });
          break;
        case 'chatgpt-set-model':
          setCurrentChatGPTModel(data.model);
          break;
        case 'chatgpt-set-model-if-need':
          const { model, features } = data;
          const modelConfig = getChatModelAndConfig();
          // 当features存在元素，且当前modelConfig不包含features时，更新model
          if (features && features.length && features.some((feature) => !modelConfig.features?.includes(feature))) {
            setCurrentChatGPTModel(model);
          }
          break;
        case 'chatgpt-set-prompt':
          setPrompt(data);
          break;
        case 'chatgpt-unset-prompt':
          unsetPrompt(data);
          break;
        case 'chatgpt-get-prompt':
          postPromptListMessageToWebview(data, { showPromptList: true });
          break;
        case 'chatgpt-set-theme':
          setCurrentChatTheme(data);
          break;
        case 'chatgpt-set-conversation':
          setCurrentChatConversation(data);
          break;
        case 'chatgpt-set-context':
          setCurrentChatContext(data);
          break;
        case 'chatgpt-open-url':
          openInBrowser(data);
          break;
        case 'chatgpt-logout':
          updateLoginStatus(false);
          vscode.commands.executeCommand('JoyCode.LogOut');
          break;
        case 'chatgpt-feedback':
          openInBrowser('https://joyspace.jd.com/sheets/AJRcr7aiRY0taDjA4UXO');
          break;
        case 'clear-current-chatgpt': // 清楚当前会话
          postMessageToWebview({
            type: 'COMMON',
            payload: {
              type: 'clearQuestionList',
              data: { msg: '清除消息' },
              isLeft: true,
            },
          });
          chatGptWebviewMessageCache = [];
          if (options.webviewView) {
            chatGptWebviewLeftLoaded = true;
          }
          // 清除面板
          handleAskChatGPT(WELCOME_QUESTION, '', options);
          break;
        case 'chatgpt-webview-insertcode':
          const editor = vscode.window.activeTextEditor || getLastAcitveTextEditor();
          if (!editor) return Logger.showErrorMessage('请先在编辑器中选中要插入的位置~');

          const selection: vscode.Selection = editor.selection;
          editor.edit((editBuilder) => {
            // 标记AI生成代码
            AdoptResultCache.setRemote(
              data.text,
              getChatModelAndConfig().label,
              AdoptResultCache.ADOPT_CODE_SOURCE.CHAT_INSERT,
              data.conversationId
            );
            editBuilder.insert(selection.start, data.text);
          });
          break;
        case 'chatgpt-webview-previewhtml':
          openHtmlCodeInVscodeWithBrowser(data);
          break;
        case 'chatgpt-webview-insertbash':
          const terminal = vscode.window.activeTerminal || vscode.window.createTerminal({ name: 'JoyCode' });
          terminal.show();
          terminal.sendText(data, false);
          break;
        case 'chatgpt-webview-insertfile':
          const { content, language, conversationId } = data as {
            content: string;
            language: string;
            conversationId: string;
          };
          await vscode.commands.executeCommand('workbench.action.files.newUntitledFile');
          const fileEditor = vscode.window.activeTextEditor;
          if (!fileEditor) return;
          fileEditor.edit((editBuilder) => {
            // 标记AI生成代码
            AdoptResultCache.setRemote(
              content,
              getChatModelAndConfig().label,
              AdoptResultCache.ADOPT_CODE_SOURCE.CHAT_NEW_FILE,
              conversationId
            );
            editBuilder.insert(fileEditor.selection.start, content);
            if (language) {
              vscode.languages.setTextDocumentLanguage(fileEditor.document, language.toLowerCase());
            }
          });
          break;
        case 'chatgpt-webview-report':
          const reportData = data as IActionCustomReportParam;
          reportAction(reportData);
          if ([ActionType.aCopy, ActionType.copy].indexOf(reportData.actionType) >= 0) {
            AdoptResultCache.setRemote(
              reportData.result || '',
              reportData.model || getChatModelAndConfig().label,
              reportData.actionType === ActionType.aCopy
                ? AdoptResultCache.ADOPT_CODE_SOURCE.CHAT_CLICK_COPY
                : AdoptResultCache.ADOPT_CODE_SOURCE.CHAT_COPY,
              reportData.conversationId
            );
          }
          break;
        case 'chatgpt-webview-message':
          Logger.showErrorMessage(data);
          break;
        case 'security-check-status':
          data.text && vscode.window.setStatusBarMessage(data.text, 3000);
          break;
        case 'chatgpt-webview-loaded':
          if (!!options.webviewView) {
            chatGptWebviewLeftLoaded = true;
          }
          // 更新GPT配置信息
          postCahceMessageToWebview();
          // 更新提示词收藏状态
          postPromptListMessageToWebview('');
          eventEmitter.emit('webviewLoaded', true);
          break;
        case 'chatgpt-get-history': // 调用history接口获取当前会话记录
          getBatchChatHistory(data, (list) => {
            postMessageToWebview({
              type: 'COMMON',
              payload: {
                type: 'updateCurrentQaList',
                data: { list: list || [] },
                isLeft: !!options.webviewView,
              },
            });
            if (!list || !list.length) {
              askChatGPT_ToAsk({ pageTitle: options.pageTitle, webviewView: options.webviewView });
            }
          });
          break;
        case 'chatgpt-download-history': // 返回所有历史记录用于下载
          getBatchChatHistory(data, (list) => {
            postMessageToWebview({
              type: 'COMMON',
              payload: {
                type: 'updateAllQaList',
                data: { list: list || [] },
                isLeft: !!options.webviewView,
              },
            });
          });
          break;
        case 'chatgpt-save-chat-prompt': // 保存会话记录
          saveChatPrompt(data);
          break;
        case 'chatgpt-get-history-items': // 获取会话记录
          const chatContent = await getChatPrompt(data);
          postMessageToWebview({
            type: 'COMMON',
            payload: {
              type: 'updateChatContent',
              data: { chatContent },
              isLeft: !!options.webviewView,
            },
          });
          break;
        case 'chatgpt-new-chat':
          askChatGPT_ToAsk({ pageTitle: options.pageTitle, webviewView: options.webviewView });
          break;
        case 'chatgpt-tool-box': // 执行工具箱操作
          executeToolBox(data);
          break;
        case 'chatgpt-set-searchapikey': // 设置searpapi密钥
          const searchConfig = getVscodeConfig('JoyCode.config.searchApiKey');
          setVscodeConfig('JoyCode.config.searchApiKey', { ...searchConfig, ...data });
          break;
        case 'chatgpt-set-customhelper': // 设置个性助手
          handleSettingChatGPT('JoyCode.config.customHelper');
          break;
        case 'chatgpt-code-review': // 代码评审
          askChatGPT_simplePrompt(RightMenuChatType.codeReview, '选中代码进行代码评审');
          break;
        case 'CHECK_LOGIN_STATUS': // 登录监测
          const isLogin = await checkLogin();
          updateLoginStatus(isLogin);
          break;
        case 'chatgpt-get-files':
          postMessageToWebview({
            type: 'COMMON',
            payload: {
              type: 'updateChatGPTFiles',
              data: { files: [] },
              isLeft: !!options.webviewView,
            },
          });
          break;
        case 'OPEN_FILE_IN_VSCODE': //  打开文件
          const { filePath } = data;
          const fileUri = vscode.Uri.file(filePath);
          // 打开文件
          const fileDoc = await vscode.workspace.openTextDocument(fileUri);
          // 显示文件内容
          await vscode.window.showTextDocument(fileDoc, { viewColumn: vscode.ViewColumn.Active });
          break;
        case 'get-feedback-config': // 点踩反馈内容
          const feedbackConf = await getFeedbackConfig();
          postMessageToWebview({
            type: 'COMMON',
            payload: {
              type: 'updateFeedbackConf',
              data: { feedbackConf },
              isLeft: !!options.webviewView,
            },
          });
          break;
        // case 'open-auto-code':
        //   await vscode.commands.executeCommand('joycoder.joycoder.popoutButtonClicked');
        //   break;
        default:
          break;
      }
    },
    onDidDispose() {
      if (!chatGptWebviewLeft) {
        abortController?.abort();
        chatGptWebviewMessageCache = [];
      }
      GlobalState.update('JoyCode.isCodeReviewing', false);
      lsFnCodelensLoading = false;
    },
  });
  if (options.webviewView) {
    chatGptWebviewLeft = webviewLoader;
  }
  // 实际调用GPT接口的message
  const textQuestion = !code ? prompt : `${prompt}: \n\`\`\`\n${code} \n\`\`\``;
  let question: string | MessageContent[] = textQuestion;
  // 必须同时满足存在图片，且模型支持vision
  if (options.imageUrlList && options.imageUrlList.length && modelConfig.features?.includes('vision')) {
    question = options.imageUrlList.map((url) => ({
      type: 'image_url',
      image_url: {
        url,
        detail: 'high',
      },
    }));
    if (textQuestion) {
      question.push({
        type: 'text',
        text: textQuestion,
      });
    }
  }

  const notWelcome = options.type !== RightMenuChatType.welcome;
  const data = { prompt, code, question, parentMessageId: options.parentMessageId };

  // 4. postMessage问题到webview
  postMessageToWebview({
    type: 'COMMON',
    payload: {
      type: ChatGPTWebviewReceiveEvent.addQuestion,
      data,
      isLeft: !!options.webviewView,
    },
  });

  // 提问时更新1次配置项，用于用户手动在setting.json中更新配置的
  postConfigMessageToWebview();

  reportRd(25);

  try {
    // 内置prompt不要让用户看到，需要在postMessageToWebview（addQuestion）之后再修改question
    const transformText = constant(options.type, options);
    if (transformText) question = !code ? transformText : `${transformText}: \n\n\`\`\`\n${code} \n\`\`\``;

    // 5. 发送请求
    abortController = new AbortController();
    GlobalState.update('JoyCode.isCodeReviewing', true);
    chatGptLeftAsked = !!options.webviewView;
    // eslint-disable-next-line init-declarations
    let response: Partial<ChatGPTAskResponseData & IResponse>;

    const progressOptions = {
      abortController,
      onProgress(response) {
        if (response.fullText || response.reasoningContent) {
          // 6. postMessage回答到webview
          const selectedFileList =
            (options?.selectedFileList || [])?.length > 0 ? options?.selectedFileList : response.selectedFileList;
          const data = {
            response: response.fullText,
            reasoningContent: response.reasoningContent,
            id: response.id,
            messageId: response.id,
            conversationId: response.conversationId,
            selectedFileList,
            ...modelResult,
          };
          response.repositoryMessage && (data['agentLogInfo'] = response.repositoryMessage);

          postMessageToWebview({
            type: 'COMMON',
            payload: {
              type: 'addResponsePending',
              data,
              isLeft: !!options.webviewView,
            },
          });
        } else if (response.repositoryMessage) {
          const data = {
            response: '',
            agentLogInfo: response.repositoryMessage,
            ...modelResult,
          };
          postMessageToWebview({
            type: 'COMMON',
            payload: {
              type: 'addResponsePending',
              data,
              isLeft: !!options.webviewView,
            },
          });
        }
      },
    };

    // 非文生图表的自定义GPT，走老agent模式
    if (options?.type === ChatGPTWebviewSendEvent.agent && options.scene !== 'text2chart') {
      // 处理Agent类型请求
      //agent请求处理
      response = await agentRequest({
        options: {
          chatModelOptions: {},
          systemMessage: options.systemMessage,
          withChatHistory: GlobalState.get('chatCurrentIsConversation') === true,
          tools: options.agentTools,
        },
        params: {
          sessionId: options.sessionId ?? '',
          abortController,
          prompt: question,
          onProgress(response) {
            const data = {
              response: response.fullText ?? '',
              agentLogInfo: response.agentLogInfo,
              messageId: response.id || uniqueId(),
              ...modelResult,
            };
            // 6. postMessage回答到webview
            postMessageToWebview({
              type: 'COMMON',
              payload: {
                type: 'addResponsePending',
                data,
                isLeft: !!options?.webviewView,
              },
            });
          },
        },
      });

      // 处理提前中止的事件中已经抛出了addResponseEnd事件
      if (response.error?.message == 'AbortError') {
        return;
      }
    } else if (options?.type === ChatGPTWebviewSendEvent.D2C) {
      if (handleVsCodeVersionCheck(modelResult, options)) return;
      let agentRes = '';
      if (code) {
        agentRes = await chatD2CVscode(code as string, {
          abortController,
          prompt,
          onProgress: (info) => {
            const data = {
              response: '',
              agentLogInfo: info,
              ...modelResult,
            };
            // 6. postMessage回答到webview
            postMessageToWebview({
              type: 'COMMON',
              payload: {
                type: 'addResponsePending',
                data,
                isLeft: !!options?.webviewView,
              },
            });
          },
        });
      }
      // 处理提前中止的事件
      if (!agentRes) {
        return;
      }
      response = {
        fullText: agentRes || '',
        id: uniqueId(),
        messageId: uniqueId(),
        conversationId: '',
        agentLogInfo: null,
      };
    } else if (options?.type === ChatGPTWebviewSendEvent.multiAgent) {
      // 多Agent
      if (handleVsCodeVersionCheck(modelResult, options)) return;
      let multiAgentLog = '';
      const agentRes = await chatAgentVscode(
        Array.isArray(question) ? question.join('\n\n') : question,
        {
          ...options,
          abortController,
          withChatHistory: false, //暂不支持历史记录
        },
        (info) => {
          multiAgentLog += info + '\n';
          const data = {
            response: '',
            agentLogInfo: multiAgentLog,
            ...modelResult,
          };
          // 6. postMessage回答到webview
          postMessageToWebview({
            type: 'COMMON',
            payload: {
              type: 'addResponsePending',
              data,
              isLeft: !!options?.webviewView,
            },
          });
        }
      );

      response = {
        fullText: agentRes?.solve?.result || '',
        id: uniqueId(),
        messageId: uniqueId(),
        conversationId: '',
        agentLogInfo: null,
      };

      // 处理提前中止的事件中已经抛出了addResponseEnd事件
      if (response.error?.message == 'AbortError') {
        handleEndChatGPT({ isLeft: !!options.webviewView, data: { ...modelResult } });
        return;
      }
    } else if (options.type === RightMenuChatType.codeShenYiFix) {
      // 神医安全检查
      reportAction({
        actionCate: 'sec',
        actionType: ActionType.eRightKey,
      });
      response = await askSecGPT({ code: code as string }, progressOptions);
    } else {
      response = await new ChatProxyService().invoke(
        {
          message: question,
          scene: Scene.Dialog,
          systemMessage: options.systemMessage || '',
          conversationId: options.conversationId || '',
          parentMessageId: options.parentMessageId || '',
        },
        progressOptions
      );
    }

    // const { drawResults, drawScript } = GlobalState.get('mermaidImg') ?? {};
    // 7. postMessage回答到webview
    const selectedFileList =
      (options?.selectedFileList || [])?.length > 0 ? options?.selectedFileList : response.selectedFileList;
    postMessageToWebview({
      type: 'COMMON',
      payload: {
        type: 'addResponseEnd',
        data: {
          response: response.fullText,
          reasoningContent: response.reasoningContent,
          id: response.id,
          messageId: response.id,
          conversationId: response.conversationId,
          prompt: question,
          agentLogInfo: response.agentLogInfo || response.repositoryMessage,
          selectedFileList,
          ...modelResult,
          // 如果是「右键神医大模型修复」的场景，打个标记给Webview，Webview内不要触发检查
          noSecCheck: options.type === RightMenuChatType.codeShenYiFix,
        },
        isLeft: !!options.webviewView,
      },
    });
    GlobalState.update('JoyCode.isCodeReviewing', false);

    reportUmp(24, 2);
    if (notWelcome) {
      try {
        // 你好的场景，不做数据上报
        let actionType = CHAT_TYPE_TO_REPORT_ACTION_TYPE[options.type || ''] || ActionType.chatQ;
        if (options.scene && SCENE_TYPE_TO_REPORT_ACTION_TYPE[options.scene]) {
          // 优先上报Agent
          actionType = SCENE_TYPE_TO_REPORT_ACTION_TYPE[options.scene];
        }
        reportUmp(24, 3);
        reportAction({
          actionCate: 'ai',
          actionType,
          question: typeof question === 'string' ? question : JSON.stringify(question),
          result: response.fullText,
          conversationId: response.conversationId,
          model: modelConfig.label,
          consumeTime: Date.now() - startTime,
          extendMsg: {
            messageId: response.id || uniqueId(),
          },
        });
      } catch (error) {
        reportUmp(24, 4);
      }
    }

    return response;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    // 7. postMessage回答到webview
    GlobalState.update('JoyCode.isCodeReviewing', false);
    postMessageToWebview({
      type: 'COMMON',
      payload: {
        type: 'addResponseEnd',
        data: {
          ...modelResult,
          selectedFileList: [],
          appendResponse: `发生异常，${
            isBusiness()
              ? '请截图反馈客服或插件管理员'
              : '请加咚咚群交流反馈（[1026086734](http://joycoder.jd.com?timeline=1)） 😁'
          }\n\n错误信息：${error?.message}`,
        },
        isLeft: !!options.webviewView,
      },
    });

    const choose = await vscode.window.showWarningMessage(
      `Error request: ${error?.message}`,
      { modal: true },
      '刷新缓存试试'
    );
    if (choose === '刷新缓存试试') {
      handleAskChatGPT(prompt, code, { ...options, conversationId: '', parentMessageId: '' });
    }
  }
}
/**
 * 通用请求插件菜单调用ChatGPT
 *
 * @export
 * @param {string} userInput
 * @param {AskChatType} type
 * @param {((vscode.TextLine & { functionContent?: string }) | null)} [menusData]
 * @param {({ webviewView?: vscode.WebviewView; pageTitle?: string | undefined })} [options]
 * @return {*}
 */
export async function askChatGPT(
  userInput: string,
  type: AskChatType,
  menusData?: (vscode.TextLine & { functionContent?: string; code?: string | undefined }) | null,
  options?: { webviewView?: vscode.WebviewView; pageTitle?: string | undefined; diffCode?: string | undefined }
) {
  const editor = vscode.window.activeTextEditor;
  let code: string = editor?.document.getText(vscode.window.activeTextEditor?.selection) || '';

  if (type === RightMenuChatType.welcome && userInput === WELCOME_QUESTION) {
    return handleAskChatGPT(WELCOME_QUESTION, '', { ...options, type });
  }

  // const side = getVscodeConfig('JoyCode.config.chatSide');
  if (!options?.webviewView || !chatGptWebviewLeft) {
    const opt = await getActiveWebview();
    if (opt) {
      const opts = { ...(options || {}), ...opt };
      options = opts;
    }
  }
  if (!options?.webviewView && !isBusiness()) return;
  // 提取函数中的代码块
  if ((CodeLensMenuChatType[type] || CodeReviewChatType[type]) && menusData?.functionContent) {
    code = menusData?.functionContent;
    if (CodeReviewChatType[type] && options) {
      options['diffCode'] = menusData?.functionContent ?? '';
      options['crCode'] = menusData?.code ?? '';
    }
  }

  // 白名单无校验code逻辑
  const isNeedCheckSelectCode = ![RightMenuChatType.terminalExplain].includes(type as RightMenuChatType);
  if (isNeedCheckSelectCode && !code) {
    GlobalState.update('JoyCode.isCodeReviewing', false);
    Logger.showInformationMessage('请先选中要操作的代码块~');
    return;
  }

  if (options?.webviewView) {
    // 通知左侧webview的tab切换到AI助手
    showAIChatView();
  }

  return handleAskChatGPT(userInput, code, { ...options, type });
}

// 执行工具箱内容
function executeToolBox(data: { url: string; command: string }) {
  if (data?.url) {
    openInBrowser(data?.url);
  } else if (data?.command) {
    if (data?.command == 'JoyCode.LogOut') {
      updateLoginStatus(false);
    }
    vscode.commands.executeCommand(data?.command);
  }
}

export async function setCurrentChatGPTModel(data: ChatModel) {
  if (data) {
    setChatModel(data);
    postConfigMessageToWebview({ model: data });
    return;
  }

  const chatGPTModelConfigs: ChatModelConfig[] = getRemoteConfigSync().chatGPTModelConfigs ?? [];
  // 过滤掉隐藏的模型，一些历史过期模型逐步淘汰
  const items = chatGPTModelConfigs
    .filter((modelConfig) => !modelConfig.hidden)
    .map((modelConfig) => ({ label: modelConfig.label, description: modelConfig.description }));
  const chatModelConfigsTest = isBusiness() ? '[]' : getVscodeConfig('JoyCode.config.test-llm-list');

  setGPTConfig({
    key: 'model',
    items: [...items, ...JSON.parse(chatModelConfigsTest)],
    placeholder: '选择模型',
    getConfig: () => getChatModel(),
    setConfig: (label) => setChatModel(label as string),
    statusBarText: (label) => `大模型已切换至${label}`,
    // wq.webmonitor.hibox.chatgpt.setting.model
    report: (label: string) => reportUmp(36, 1, label),
  });
}

enum Theme {
  Auto = 'Auto',
  Light = 'Light',
  Dark = 'Dark',
}
/**
 * 修改主题
 * @param theme
 */
async function setCurrentChatTheme(theme: Theme) {
  // wq.webmonitor.hibox.chatgpt.setting.theme
  reportUmp(36, 4, theme);
  GlobalState.update('chatCurrentTheme', theme);
  // 修改主题后需要左右同时生效
  postConfigMessageToWebview({ theme: theme });

  const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
  statusBarItem.text = `已切换主题至 ${theme}`;
  statusBarItem.show();
  setTimeout(() => {
    statusBarItem.hide();
  }, 3000);
}

async function setGPTConfig(options: {
  key: 'model' | 'theme' | 'conversation';
  items: Array<{ label: string; description?: string }>;
  placeholder: string;
  getConfig: () => string;
  setConfig: (config: string | boolean | number) => void;
  transformToConfig?: (label: string) => string | boolean | number;
  statusBarText: (label: string) => string;
  isNotSet?: (label: string) => boolean;
  report: (label: string) => void;
}) {
  const quickPick = vscode.window.createQuickPick();
  quickPick.items = options.items;
  quickPick.activeItems = quickPick.items.filter(
    (item) => (options.transformToConfig?.(item.label) || item.label) === options.getConfig()
  );
  quickPick.placeholder = options.placeholder;
  quickPick.show();

  const newCurrent: string | undefined = await new Promise((resolve) => {
    quickPick.onDidAccept(() => resolve(quickPick.selectedItems[0].label));
    quickPick.onDidHide(() => resolve(undefined));
  });

  quickPick.dispose();

  if (!newCurrent) return;

  if (options.isNotSet?.(newCurrent)) return;

  options.report(newCurrent);

  options.setConfig(options.transformToConfig ? options.transformToConfig(newCurrent) : newCurrent);
  postConfigMessageToWebview({ [options.key]: newCurrent });

  const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
  statusBarItem.text = options.statusBarText(newCurrent) || newCurrent;
  statusBarItem.show();
  setTimeout(() => {
    statusBarItem.hide();
  }, 3000);
}

async function setCurrentChatConversation(flag?: '开启' | '关闭') {
  if (flag) {
    GlobalState.update('chatCurrentIsConversation', flag == '开启');
    postConfigMessageToWebview({ conversation: flag });
    return;
  }
  setGPTConfig({
    key: 'conversation',
    items: [
      {
        label: '开启',
        description: '每次请求携带历史对话记录',
      },
      {
        label: '关闭',
        description: '忽略历史对话记录，一问一答',
      },
    ],
    placeholder: '开关多轮对话模式',
    getConfig: () => GlobalState.get('chatCurrentIsConversation') ?? true,
    setConfig: (label) => GlobalState.update('chatCurrentIsConversation', label),
    transformToConfig: (label: string) => label == '开启',
    statusBarText: (label: string) => `已${label}多轮对话`,
    // wq.webmonitor.hibox.chatgpt.setting.Conversation
    report: (label: string) => reportUmp(36, 5, label),
  });
}

async function setCurrentChatContext(flag) {
  GlobalState.update('chatCodeContext', flag);
  postConfigMessageToWebview({ context: flag });
  eventEmitter.emit('chatCodeContext', flag);
  return;
}

/**
 * 代码翻译调用GPT
 *
 * @export
 * @param {string} type
 * @return {*}
 */
export async function askChatGPT_transformCode(type: CodeTranslateChatType) {
  await setSelectChatSide();
  if (type === 'HTMLOptimization') {
    return await askChatGPT(`帮我优化以下HTML代码语义?`, type);
  }
  return await askChatGPT(`帮我把以下代码转为${type}?`, type);
}

/**
 * 行间菜单调用ChatGPT
 *
 * @param type - 菜单类型。
 * @param prompt - 提示信息。
 * @param menusData - 包含行数据和可选的函数内容。
 */
export async function askChatGPT_codeLensMenu(
  type: CodeLensMenuChatType,
  prompt: string,
  menusData: vscode.TextLine & { functionContent?: string }
) {
  if (lsFnCodelensLoading) {
    Logger.showInformationMessage('其他行间菜单正在执行，请稍后~');
    return;
  }
  try {
    await setSelectChatSide();
    lsFnCodelensLoading = true;
    await askChatGPT(prompt, type, menusData);
  } catch (error) {
    Logger.error(error);
  } finally {
    lsFnCodelensLoading = false;
  }
}

export function getChatGptWebviewLeftLoaded() {
  return chatGptWebviewLeftLoaded;
}

/**
 * 右键菜单调用ChatGPT
 *
 * @param type - 菜单类型。
 * @param prompt - 提示信息。
 * @param menusData - 包含行数据和可选的函数内容。
 */
export async function askChatGPT_simplePrompt(type: RightMenuChatType, prompt: string) {
  await setSelectChatSide();
  await askChatGPT(prompt, type);
}

// 0. 你好，只做唤起webview的
export const askChatGPT_ToAsk = async (options?: { webviewView?: vscode.WebviewView; pageTitle: string | undefined }) =>
  await askChatGPT(WELCOME_QUESTION, RightMenuChatType.welcome, null, options);
// 单文件代码评审
export const askChatGPT_ToCR = async (
  crCode: { diffCode: string | undefined; code: string | undefined },
  options?: { webviewView?: vscode.WebviewView; pageTitle: string | undefined; type?: string }
) => {
  const menusData = { functionContent: crCode?.diffCode, code: crCode?.code } as
    | (vscode.TextLine & { functionContent?: string; code?: string })
    | null;
  return await askChatGPT('代码审核', CodeReviewChatType.codeReviewOneFile, menusData, options);
};

// 1. AI助手-其它
export const askChatGPT_ByConfigText = async () => {
  const chatGPTConfigList = (getRemoteConfigSync().chatGPTModelConfigs || []).map((item: any) => ({
    handleText: item.label,
  }));
  const extraConfigList = [
    {
      handleText: '自定义',
    },
  ];
  const wholeList = chatGPTConfigList.concat(extraConfigList);

  let selLabel = await vscode.window.showQuickPick(wholeList.map((item: any) => item.handleText));

  if (!selLabel) return;

  if (selLabel == '自定义') {
    const result = await vscode.window.showInputBox({
      placeHolder: '示例：解释一下下面代码',
    });
    if (!result) return;

    selLabel = result;
  }

  await setSelectChatSide();
  await askChatGPT(selLabel, RightMenuChatType.other);
};

export async function callLLM(message, modelConfig?: any, abortController?: AbortController) {
  if (!modelConfig) {
    modelConfig = {};
  }
  if (!modelConfig.model) {
    modelConfig.model = getChatModelAndConfig().label;
  }
  const result = await new ChatProxyService().invoke(
    {
      message,
      scene: Scene.Cursor,
      ...modelConfig,
    },
    {
      abortController,
      onProgress: () => {},
    }
  );
  return result;
}

export default function (context: vscode.ExtensionContext) {
  try {
    DataSetsAPI.getDataSets();
  } catch (error) {
    console.warn('%c [ old-chat-error ]-1270', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
  context.subscriptions.push(
    // 监听VSCode配置变更事件
    vscode.workspace.onDidChangeConfiguration((event) => {
      // 检查是否是JoyCode.enableBrowserTool配置变更
      if (event.affectsConfiguration('JoyCode.enableBrowserTool') && settingWebview) {
        // 当浏览器工具配置变更时，重新调用setSettingWebviewData函数
        // 这样会从VSCode配置中读取最新的值，然后发送给设置页面
        setSettingWebviewData(settingWebview);
      }
      if (event.affectsConfiguration('JoyCode.config.todoListEnabled') && settingWebview) {
        // 当todolist配置变更时，重新调用setSettingWebviewData函数
        setSettingWebviewData(settingWebview);
      }
    }),
    // 监听选择代码事件
    vscode.window.onDidChangeTextEditorSelection(
      debounce(() => {
        const selectText = getSelectedText();
        const selectInfo = getSelectionInfo();
        postMessageToWebview({
          type: 'COMMON',
          payload: {
            type: 'updateContextPanel',
            data: { content: selectText ?? '', selectInfo },
            isLeft: true,
          },
        });
      }, 100)
    ),
    // 注册设置页面mcp 相关事件
    vscode.commands.registerCommand('JoyCode.config.setting.mcp', async (data) =>
      mcpExecuteCommand(settingWebview, data)
    ),

    // 设置页面
    vscode.commands.registerCommand('JoyCode.config.setting', async (data) => {
      if (settingWebview) {
        setSettingWebviewData(settingWebview, data);
        settingWebview.reveal(vscode.ViewColumn.One);
        return;
      }

      // 1. 静态资源根目录
      const distSettingRoot = path.join(context.extensionPath, 'dist', 'setting');

      // 2. 创建 webview 面板，并指定 localResourceRoots
      settingWebview = vscode.window.createWebviewPanel('setting', 'JoyCode 设置', vscode.ViewColumn.One, {
        enableScripts: true,
        localResourceRoots: [vscode.Uri.file(distSettingRoot)],
        retainContextWhenHidden: true,
      });

      // 3. 获取 HTML 文件内容
      const htmlUri = vscode.Uri.file(path.join(distSettingRoot, 'index.html'));
      const htmlUint8Array = await vscode.workspace.fs.readFile(htmlUri);
      let html = Buffer.from(htmlUint8Array).toString('utf8');

      // 4. 替换 HTML 中的本地静态资源路径为 asWebviewUri 路径
      html = html.replace(/(src|href)=["']([^"']+)["']/g, (match, p1, p2) => {
        // 跳过 http/https/协议开头的资源
        if (/^(http|https|\/\/)/.test(p2)) return match;
        // 去掉路径前的 ./ 或 /
        const relPath = p2.replace(/^\.?\//, '');
        // 拼接本地资源绝对路径
        const resourcePath = vscode.Uri.file(path.join(distSettingRoot, relPath));
        const webviewUri = settingWebview?.webview.asWebviewUri(resourcePath);
        return `${p1}="${webviewUri}"`;
      });

      // 5. 设置 webview 内容
      settingWebview.webview.html = html;

      // 6. 监听 panel 被关闭，销毁引用
      settingWebview.onDidDispose(
        () => {
          settingWebview = undefined;
        },
        null,
        context.subscriptions
      );

      // 7. 接收来自 webview 的消息
      const postCodebaseIndexingProcess = (codebaseProgress: CodebaseProgress) => {
        settingWebview?.webview.postMessage({
          type: 'COMMON',
          payload: {
            type: 'codebase-update-progress',
            data: {
              codebaseIndexingProgress: codebaseProgress.progress,
              codebaseIndexingStatus: codebaseProgress.status,
            },
            isLeft: data === undefined || !!data,
          },
        });
      };
      let codebaseIndexingProgressCallbackRegistered = false;
      const registerCodebaseIndexingProgressCallback = async () => {
        if (codebaseIndexingProgressCallbackRegistered) return;
        codebaseIndexingProgressCallbackRegistered = true;
        console.log('[Codebase]进度更新回调函数已注册');
        await CodebaseManager.getProgressCallback({
          onProgressChange: postCodebaseIndexingProcess,
          onComplete: postCodebaseIndexingProcess,
        });
      };
      let refreshClearup: () => void;
      settingWebview.webview.onDidReceiveMessage(
        async (message) => {
          // console.log('[消息接收]：', message?.payload?.data);
          switch (message?.payload?.type) {
            case 'setting-mcp':
              // 调用设置页面mcp相关接口
              mcpWebViewCommand(message.payload?.data);
              break;
            case 'setting-connect-verify':
              // 初始化setting webview
              setSettingWebviewData(settingWebview, data);
              break;
            case 'chatgpt-setting-info':
              setConfigInfo(message?.payload?.data, false);
              break;
            case 'settiing-logout':
              updateLoginStatus(false);
              vscode.commands.executeCommand('JoyCode.LogOut');
              break;
            case 'settiing-login':
              forceJdhLogin(false, () => {
                setSettingWebviewData(settingWebview, data);
              });
              break;
            case 'joycoder-set-mode':
              console.log('[Mode]接收到模式切换请求', message.payload.data);
              break;
            case 'codebaseIndexing':
              const action = message.payload.data.action;
              console.log('[Codebase]接收到webview请求', action);
              switch (action) {
                case 'start':
                  postCodebaseIndexingProcess({
                    progress: 0,
                    status: CodebaseIndexingStatus.PREPARING,
                  });
                  await CodebaseManager.startIndex();
                  await registerCodebaseIndexingProgressCallback();
                  break;
                case 'cancel':
                  await CodebaseManager.cancelIndex();
                  // 其实cancel了进度还在，已经索引的东西也没删，但是就处理成进展归零了
                  postCodebaseIndexingProcess({
                    progress: 0,
                    status: CodebaseIndexingStatus.UNINDEXED,
                  });
                  break;
                case 'remove':
                  await CodebaseManager.removeIndex();
                  postCodebaseIndexingProcess({
                    progress: 0,
                    status: CodebaseIndexingStatus.UNINDEXED,
                  });
                  break;
                case 'getProgress':
                  const codebaseIndexingProgress = CodebaseManager.getLocalIndexingStatus();
                  if (codebaseIndexingProgress.status !== CodebaseIndexingStatus.UNINDEXED) {
                    await registerCodebaseIndexingProgressCallback();
                  }
                  postCodebaseIndexingProcess(codebaseIndexingProgress);
                  break;
              }
              break;
            case 'setting-mode-operation':
              // 处理多模式操作
              const modeAction = message.payload.data.action;
              const modeData = message.payload.data;
              console.log('[Mode]接收到多模式操作请求', modeAction, modeData);

              try {
                const joyCoderProvider = JoyCoderProvider.getVisibleInstance();
                if (!joyCoderProvider) {
                  console.error('[Mode]未找到可用的 JoyCoderProvider 实例');
                  break;
                }

                switch (modeAction) {
                  case 'switch':
                    // 切换模式
                    if (modeData.modeId) {
                      await joyCoderProvider.handleModeSwitch(modeData.modeId);
                      console.log('[Mode]模式切换成功:', modeData.modeId);
                    }
                    break;
                  case 'updateCustomMode':
                    if (modeData.modeConfig) {
                      const joyCoderProvider = JoyCoderProvider.getVisibleInstance();
                      await joyCoderProvider?.customModesManager?.updateCustomMode(
                        modeData.modeConfig.agentId,
                        modeData.modeConfig
                      );
                      // Update state after saving the mode
                      const customModes = await joyCoderProvider?.customModesManager?.getCustomModes();
                      await joyCoderProvider?.updateGlobalState('customModes', customModes);
                      await joyCoderProvider?.updateGlobalState('mode', modeData.modeConfig.agentId);
                      await setSettingWebviewData(settingWebview, data);
                    }
                    break;
                  case 'deleteCustomMode':
                    // 删除自定义模式
                    if (modeData.agentId) {
                      const answer = await vscode.window.showInformationMessage(
                        '确定要删除自定义模式吗？',
                        { modal: true },
                        '是'
                      );
                      if (answer !== '是') {
                        break;
                      }
                      const joyCoderProvider = JoyCoderProvider.getVisibleInstance();
                      await joyCoderProvider?.customModesManager?.deleteCustomMode(modeData.agentId);
                      // Switch back to default mode after deletion
                      await joyCoderProvider?.updateGlobalState('mode', defaultModeSlug);
                      setSettingWebviewData(settingWebview, data);
                    }
                    break;
                  case 'openFile':
                    if (modeData.agentTile === '新建智能体') {
                      vscode.window.showErrorMessage('请先创建智能体');
                      break;
                    }

                    const workspaceFolders = vscode.workspace.workspaceFolders;
                    if (workspaceFolders && workspaceFolders.length > 0) {
                      // 直接传递原始路径给 openFile 函数，让它处理路径解析
                      if (modeData.filePath) {
                        const workspaceRoot = getWorkspacePath();
                        // 在远程环境下，workspaceRoot 应该是 vscode.Uri 对象
                        const workspaceUri =
                          workspaceRoot instanceof vscode.Uri ? workspaceRoot : vscode.Uri.parse(workspaceRoot);
                        const roomodesUri = vscode.Uri.joinPath(workspaceUri, modeData.filePath);
                        openFile(isRemoteEnvironment() ? roomodesUri.toString() : modeData.filePath);
                      }
                    } else {
                      vscode.window.showErrorMessage(JoyCoderMCPMessageMap['No workspace folder open']);
                    }
                    break;
                  case 'toggleCustomMode':
                    if (modeData.agentId && modeData.isActive !== undefined) {
                      console.log(modeData.agentId, 'set isActive', modeData.isActive);
                      await joyCoderProvider?.customModesManager?.toggleCustomMode(modeData.agentId, modeData.isActive);
                      // Update state after saving the mode
                      const customModes = (await joyCoderProvider?.customModesManager?.getCustomModes()) || [];
                      await joyCoderProvider?.updateGlobalState('customModes', customModes);
                      // await joyCoderProvider?.updateGlobalState('mode', modeData.agentId);
                      const currentMode = (await joyCoderProvider.getState()).mode;
                      currentMode === modeData.agentId && (await joyCoderProvider.handleModeSwitch(defaultModeSlug));
                      await joyCoderProvider?.postStateToWebview();
                      await setSettingWebviewData(settingWebview, { type: 'agent' });
                    }
                    break;
                  default:
                    console.warn('[Mode]未知的模式操作:', modeAction);
                }
              } catch (error) {
                console.error('[Mode]处理多模式操作失败:', error);
              }
              break;
            case 'chatgpt-setting-browser':
              //浏览器设置
              const browserData = message.payload?.data;
              if (browserData.type === 'setBrowserToolEnabled') {
                const browserToolEnabled = browserData?.browserToolEnabled;
                GlobalState.update('browserToolEnabled', browserToolEnabled);
                setVscodeConfig('JoyCode.enableBrowserTool', !!browserToolEnabled);
              }
              try {
                if (browserData.type === 'setBrowserToolViewportSize') {
                  const browserViewportSize = browserData?.browserViewportSize;
                  const viewport = JSON.parse(browserViewportSize || '{"width":900,"height":600}');
                  const browserSettings = GlobalState.get('browserSettings') || {};
                  const browserViewport = {
                    ...browserSettings,
                    viewport,
                    headless: true, // 默认不启用无头模式
                  };
                  GlobalState.update('browserSettings', browserViewport);
                }
              } catch (error) {
                Logger.log('%c [浏览器设置失败 error ]-1589', error);
              }
              if (browserData.type === 'setRemoteBrowserEnabled') {
                const remoteBrowserEnabled = browserData?.remoteBrowserEnabled;
                GlobalState.update('remoteBrowserEnabled', remoteBrowserEnabled);
                if (!remoteBrowserEnabled) {
                  GlobalState.update('remoteBrowserHost', undefined);
                }
              }
              if (browserData.type === 'setRemoteBrowserHost') {
                const remoteBrowserHost = browserData?.remoteBrowserHost;
                GlobalState.update('remoteBrowserHost', remoteBrowserHost);
              }
              break;
            case 'testBrowserConnection':
              // If no text is provided, try auto-discovery
              if (!message.text) {
                // Use testBrowserConnection for auto-discovery
                const chromeHostUrl = await discoverChromeHostUrl();

                if (chromeHostUrl) {
                  // Send the result back to the webview
                  await settingWebview?.webview.postMessage({
                    type: 'COMMON',
                    payload: {
                      type: 'browserConnectionResult',
                      data: {
                        success: !!chromeHostUrl,
                        text: `自动发现并测试了与Chrome的连接: ${chromeHostUrl}`,
                        values: { endpoint: chromeHostUrl },
                      },
                    },
                  });
                } else {
                  await settingWebview?.webview.postMessage({
                    type: 'COMMON',
                    payload: {
                      type: 'browserConnectionResult',
                      data: {
                        success: false,
                        text: '在网络上未找到Chrome实例。请确保Chrome浏览器正在运行，并且已启用远程调试功能（使用参数--remote-debugging-port=9222）。',
                      },
                    },
                  });
                }
              } else {
                // Test the provided URL
                const customHostUrl = message.text;
                const hostIsValid = await tryChromeHostUrl(message.text);

                // Send the result back to the webview
                await settingWebview?.webview.postMessage({
                  type: 'COMMON',
                  payload: {
                    type: 'browserConnectionResult',
                    data: {
                      success: hostIsValid,
                      text: hostIsValid ? `成功连接到Chrome浏览器：${customHostUrl}` : '无法连接到Chrome浏览器',
                    },
                  },
                });
              }
              break;
            case 'get-user-prompts':
              // 获取用户快捷指令
              const userPromptManager = new UserPromptManager();
              const projectPrompts = await userPromptManager.readProjectPromptFile();
              const systemPrompts = await userPromptManager.readSystemPromptFile();

              await settingWebview?.webview.postMessage({
                type: 'COMMON',
                payload: {
                  type: 'user-prompts-response',
                  data: projectPrompts,
                  source: 'project',
                },
              });
              await settingWebview?.webview.postMessage({
                type: 'COMMON',
                payload: {
                  type: 'user-prompts-response',
                  data: systemPrompts,
                  source: 'global',
                },
              });
              break;

            case 'delete-user-prompt':
              // 删除快捷指令
              const deleteManager = new UserPromptManager();
              if (message.payload.source === 'project') {
                // console.log('tryDelete:\n\n\n\n' + JSON.stringify(message.payload.data));
                await deleteManager.removeProjectUserPrompt(message.payload.data);
              } else {
                await deleteManager.removeSystemUserPrompt(message.payload.data);
              }
              // 删除后重新获取最新列表
              const updatedProjectPrompts = await deleteManager.readProjectPromptFile();
              const updatedSystemPrompts = await deleteManager.readSystemPromptFile();

              await settingWebview?.webview.postMessage({
                type: 'COMMON',
                payload: {
                  type: 'user-prompts-response',
                  data: updatedProjectPrompts,
                  source: 'project',
                },
              });
              await settingWebview?.webview.postMessage({
                type: 'COMMON',
                payload: {
                  type: 'user-prompts-response',
                  data: updatedSystemPrompts,
                  source: 'global',
                },
              });
              break;

            case 'update-user-prompt':
              // 更新快捷指令
              const updateManager = new UserPromptManager();
              if (message.payload.source === 'project') {
                await updateManager.removeProjectUserPrompt(message.payload.data.oldShortcut);
                if (message.payload.changeSource) {
                  await updateManager.addSystemUserPrompt(message.payload.data.newShortcut);
                } else {
                  await updateManager.addProjectUserPrompt(message.payload.data.newShortcut);
                }
              } else {
                await updateManager.removeSystemUserPrompt(message.payload.data.oldShortcut);
                if (message.payload.changeSource) {
                  await updateManager.addProjectUserPrompt(message.payload.data.newShortcut);
                } else {
                  await updateManager.addSystemUserPrompt(message.payload.data.newShortcut);
                }
              }

              // 更新后重新获取最新列表
              const projectPromptsAfterUpdate = await updateManager.readProjectPromptFile();
              const systemPromptsAfterUpdate = await updateManager.readSystemPromptFile();

              await settingWebview?.webview.postMessage({
                type: 'COMMON',
                payload: {
                  type: 'user-prompts-response',
                  data: projectPromptsAfterUpdate,
                  source: 'project',
                },
              });
              await settingWebview?.webview.postMessage({
                type: 'COMMON',
                payload: {
                  type: 'user-prompts-response',
                  data: systemPromptsAfterUpdate,
                  source: 'global',
                },
              });
              break;

            case 'save-user-prompt':
              // 保存新的快捷指令
              const saveManager = new UserPromptManager();
              if (message.payload.source === 'project') {
                await saveManager.addProjectUserPrompt(message.payload.data);
              } else {
                await saveManager.addSystemUserPrompt(message.payload.data);
              }

              // 保存后重新获取最新列表
              const projectPromptsAfterSave = await saveManager.readProjectPromptFile();
              const systemPromptsAfterSave = await saveManager.readSystemPromptFile();
              // console.log('project\n\n\n\nsave' + JSON.stringify(projectPromptsAfterSave));
              // console.log('system\n\n\n\nsave' + JSON.stringify(systemPromptsAfterSave));

              await settingWebview?.webview.postMessage({
                type: 'COMMON',
                payload: {
                  type: 'user-prompts-response',
                  data: projectPromptsAfterSave,
                  source: 'project',
                },
              });
              await settingWebview?.webview.postMessage({
                type: 'COMMON',
                payload: {
                  type: 'user-prompts-response',
                  data: systemPromptsAfterSave,
                  source: 'global',
                },
              });
              break;

            case 'open-memory-markdown':
              // 打开 memory.md 文件到 vscode 标签页
              let memoryPath = require('path').join(require('os').homedir(), '.joycode', 'memory', 'memory.md');
              openFile(memoryPath, 'global');
              break;

            case 'get-memory-content':
              memoryPath = require('path').join(require('os').homedir(), '.joycode', 'memory', 'memory.md');
              let content = '';

              try {
                const fileExists = await FileSystemHelper.access(memoryPath)
                  .then(() => true)
                  .catch(() => false);

                if (fileExists) {
                  content = await FileSystemHelper.readFile(memoryPath, 'utf-8');
                }
              } catch (error) {
                console.error('Error reading memory file:', error);
              }

              await settingWebview?.webview.postMessage({
                type: 'COMMON',
                payload: {
                  type: 'memory-markdown-response',
                  data: content,
                },
              });
              break;

            case 'set-memory-content':
              const memoryDir = path.join(os.homedir(), '.joycode', 'memory');
              memoryPath = path.join(memoryDir, 'memory.md');
              try {
                // 检查文件是否存在
                const fileExists = await FileSystemHelper.access(memoryPath)
                  .then(() => true)
                  .catch(() => false);

                if (!fileExists) {
                  try {
                    await FileSystemHelper.mkdir(memoryDir, { recursive: true });
                    await FileSystemHelper.writeFile(memoryPath, '');
                    console.log('Created empty memory file:', memoryPath);
                  } catch (createError) {
                    console.error('Error creating memory file:', createError);
                  }
                }

                // 将 message.payload.data 内容写入文件
                await FileSystemHelper.writeFile(memoryPath, message.payload.data);
              } catch (error) {
                console.error('Error reading memory file:', error);
              }
              break;

            case 'getKnowledgeBaseInfo':
              switch (message.payload.dataType) {
                case 'list': {
                  const list = await DataSetsAPI.getDataSets();
                  await settingWebview?.webview.postMessage({
                    type: 'COMMON',
                    payload: {
                      type: 'knowledge-base-info',
                      data: { list, dataType: message.payload.dataType },
                    },
                  });
                  break;
                }
                case 'add':
                case 'update':
                case 'delete': {
                  const baseInfo =
                    message.payload.dataType === 'add'
                      ? await DataSetsAPI.addDataSets(message.payload.data)
                      : message.payload.dataType === 'delete'
                      ? await DataSetsAPI.delDataSetsById(message.payload.data)
                      : await DataSetsAPI.updateDataSetsById(message.payload.data);
                  let list = [];
                  if (baseInfo?.code === 0) {
                    list = await DataSetsAPI.getDataSets();
                  }
                  await settingWebview?.webview.postMessage({
                    type: 'COMMON',
                    payload: {
                      type: 'knowledge-base-info',
                      data: { list, dataType: message.payload.dataType, baseInfo },
                    },
                  });
                  break;
                }
              }
              break;

            case 'getDocInfo':
              switch (message.payload.dataType) {
                case 'list': {
                  refreshClearup && refreshClearup(); // 列表切换清除刷新
                  const { page, page_size, datasetId } = message.payload.data || {};
                  await getDocList({ page, page_size, datasetId, settingWebview });
                  break;
                }
                case 'reChunk': {
                  const { document_ids, datasetId, page } = message.payload.data || {};
                  const docInfo = await DataSetsAPI.reChunkDoc({
                    dataset_id: datasetId,
                    document_ids,
                  });
                  if (docInfo.code === 0) {
                    // 每隔5秒刷新一次 刷新120秒（共24次）
                    refreshClearup = await printPeriodically(
                      async () => {
                        await getDocList({ page, page_size: 20, datasetId, settingWebview });
                      },
                      5000, // 4秒间隔
                      24 // 最多执行24次，总共120秒
                    );
                  } else if (docInfo.code === 600) {
                    const author_url = docInfo?.data
                      ? docInfo?.data?.author_url + encodeURIComponent('joycode://JoyCode.joycoder-editor/openDoc')
                      : '';
                    if (author_url) {
                      addJoyspaceRouterIn(settingWebview, async (res) => {
                        const refreshClearUp = await printPeriodically(
                          async () => {
                            await getDocList({ page, page_size: 20, datasetId, settingWebview });
                          },
                          3000, // 15秒间隔
                          20 // 最多执行20次，总共60秒
                        );
                        const authInfo = res?.meCode
                          ? await DataSetsAPI.joyspaceAuth({
                              me_code: res?.meCode,
                              me_expire_in: res?.meExpireIn,
                            })
                          : { code: 500, msg: '获取授权信息失败' };
                        await settingWebview?.webview.postMessage({
                          type: 'COMMON',
                          payload: {
                            type: 'joyspace-auth-data',
                            data: authInfo,
                          },
                        });
                        refreshClearUp && refreshClearUp(); // 停止刷新
                      }); // 打开设置选中上下文  获取meCode
                      openInBrowser(author_url);
                    }
                  }

                  break;
                }
                case 'delete':
                case 'stopChunk': {
                  const { document_id, datasetId, page } = message.payload.data || {};
                  const docInfo =
                    message.payload.dataType === 'delete'
                      ? await DataSetsAPI.delDocInfo({
                          dataset_id: datasetId,
                          id: document_id,
                        })
                      : await DataSetsAPI.stopChunk({
                          dataset_id: datasetId,
                          id: document_id,
                        });
                  if (docInfo.code === 0) {
                    await getDocList({ page, page_size: 20, datasetId, settingWebview });
                  }
                  break;
                }
                case 'normalUrl': {
                  const { docUrl, docName, datasetId, filesData, type } = message.payload.data || {};
                  const args =
                    type === 'local'
                      ? { filesData, datasetId, type }
                      : type === 'joyspace'
                      ? {
                          name: docName,
                          source_type: 'joyspace',
                          type: 'joyspace',
                          location: docUrl,
                          datasetId,
                        }
                      : { docUrl, docName, datasetId };
                  const urlData = await DataSetsAPI.getDocUrlData(args);
                  await settingWebview?.webview.postMessage({
                    type: 'COMMON',
                    payload: {
                      type: 'update-url-data',
                      data: urlData,
                    },
                  });
                  await getDocList({ page: 1, page_size: 20, datasetId, settingWebview });
                  break;
                }
              }
              break;
            default:
              console.log('消息未匹配');
          }
        },
        undefined,
        context.subscriptions
      );
    })
  );
}
export async function mcpWebViewCommand(data?: any) {
  await vscode.commands.executeCommand('JoyCode.config.setting.mcp', data);
}

export async function getActiveWebview() {
  // const side = getVscodeConfig('JoyCode.config.chatSide');
  const options = chatGptWebviewLeft?.options;
  if (chatGptLeftAsked) {
    return options;
  } else {
    if (chatGptWebviewLeft) {
      return options;
    } else {
      await vscode.commands.executeCommand('JoyCode-left-view.focus');
    }
    return options;
  }
}
export function getSettingWebview() {
  return settingWebview;
}
// 右键菜单选边
async function setSelectChatSide(): Promise<void> {
  return new Promise(async (resolve) => {
    try {
      postMessageToWebview({
        type: 'COMMON',
        payload: {
          type: 'JoyCode-chat-side',
          data: { side: '左侧' },
          isLeft: true,
        },
      });
      await vscode.commands.executeCommand('JoyCode-left-view.focus');
      resolve();
    } catch (error) {
      Logger.error('%c [ error ]-1174', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  });
}
// 通知左右聊天窗登录信息
export function updateLoginStatus(isLogin: boolean) {
  postMessageToWebview({
    type: 'COMMON',
    payload: {
      type: 'update-login-info',
      data: {
        isLogined: !!isLogin,
      },
      isLeft: true,
    },
  });
  postMessageToWebview({
    type: 'COMMON',
    payload: {
      type: 'update-login-info',
      data: {
        isLogined: !!isLogin,
      },
    },
  });
  if (isLogin) {
    codeStatService.updatePtk('updatePtk:' + (getJdhLoginInfo()?.ptKey || ''));
  }
}
export function setDecorationType(data, contentText?: string) {
  try {
    decorationType && decorationType.dispose();
    const iconFile = path.resolve(__dirname, '..', 'assets', 'translation', `loading-decoration.svg`);
    const contentIconPath = vscode.Uri.file(iconFile);
    contentText = contentText || `函数注释生成中···`;
    // contentText = (contentText || `函数注释生成中···`) + '×';
    decorationType = vscode.window.createTextEditorDecorationType({
      light: {
        // 这将应用于亮色主题
        after: {
          contentText,
          color: 'rgba(0, 0, 0, 0.4)',
          margin: '0 0 0 6px', // 根据需要调整边距
          textDecoration: 'none; cursor: pointer;',
        },
        before: {
          contentIconPath,
        },
      },
      dark: {
        // 这将应用于暗色主题
        before: {
          contentIconPath,
        },
        after: {
          contentText,
          color: 'rgba(255, 255, 255, 0.4)',
          margin: '0 0 0 6px', // 根据需要调整边距
          textDecoration: 'none; cursor: pointer;',
        },
      },
    });
    const editor = vscode.window.activeTextEditor;
    if (editor) {
      const lineNumber = data.lineNumber;
      if (lineNumber > 0 && lineNumber <= editor.document.lineCount) {
        const line = editor.document.lineAt(lineNumber - 1);
        const range = new vscode.Range(line.range.end, line.range.end);
        const markdownString = new vscode.MarkdownString(
          `[取消](command:JoyCode.codelens.del.loading?${encodeURIComponent(JSON.stringify({ data }))})`
        );
        markdownString.isTrusted = true; // 允许命令链接
        editor.setDecorations(decorationType as vscode.TextEditorDecorationType, [
          {
            range,
            hoverMessage: markdownString,
          },
        ]);
      }
    }
  } catch (error) {
    decorationType && decorationType.dispose();
  }
  return decorationType;
}
export function handleVsCodeVersionCheck(modelResult, options) {
  if (needUpdateVsCodeGuide('1.93.0')) {
    // langchain需要node18+，vscode1.82起开始用node18
    postMessageToWebview({
      type: 'COMMON',
      payload: {
        type: 'addResponseEnd',
        data: {
          appendResponse: '当前VSCode版本过低~',
          ...modelResult,
        },
        isLeft: true,
      },
    });
    return true;
  }
  return false;
}
