import React, { useState, useEffect } from 'react';
import { message, Modal, Tabs } from 'antd';
import type { TabsProps } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib/checkbox/Checkbox';
import { useHandleMessage } from '../../hooks/useHandleMessage';
import { CommonMessage } from '../../messages/messageTypes';
import { CodebaseIndexingStatus } from '@joycoder/shared/src/types/codebase';
import {
  AutoApprovalSettings,
  DEFAULT_AUTO_APPROVAL_SETTINGS,
} from '@joycoder/agent-driven/src/shared/AutoApprovalSettings';

import McpMarket from '../mcp';
import AgentManagement from '../AgentManagement';
import GeneralTab from '../General';
import SpecialTab from '../Special';
import CodebaseTab from '../Codebase';
import RulesTab from '../Rules';
import LoaclPopup from '../Codebase/localPopup';
import './index.scss';
import { ModesInfo } from '../../typings/modes';
import { McpActiveTab } from '../../typings/marketplace';
import URLPopup from '../Codebase/urlPopup';
import BasePopup from '../Codebase/knowledgeBasePopup';
import { ExclamationCircleOutlined } from '@ant-design/icons';

export default function Setting() {
  // 使用Modal.useModal()获取modal实例，这样可以自动继承ConfigProvider的前缀配置
  const [modal, contextHolder] = Modal.useModal();
  const [isShowCodeLens, setShowCodeLens] = useState(false);
  const [isCommitCodeReview, setCommitCodeReview] = useState(false);
  const [isSilentMode, setSilentMode] = useState(false);
  const [isTodoListEnable, setIsTodoListEnable] = useState(true); //todolist控制参数，初始为false，等待后端数据
  const [isErrorLine, setErrorLine] = useState(false);
  const [completionDelay, setCompletionDelay] = useState(1.2);
  const [maxConcurrentFileReads, setMaxConcurrentFileReads] = useState(1);
  const [codeLens, setCodeLens] = useState<string[]>(['functionComment']);
  const [commitMessage, setCommitMessage] = useState('GIT_SCHEMA');
  const [hasWorkspaceFolder, setHasWorkspaceFolder] = useState(false);
  const [codebaseIndexingProgress, setCodebaseIndexingProgress] = useState(0);
  const [codebaseIndexingStatus, setCodebaseIndexingStatus] = useState<CodebaseIndexingStatus>(
    CodebaseIndexingStatus.PREPARING
  );
  const [browserViewportSize, setBrowserViewportSize] = useState('1024x768');
  const [browserToolEnabled, setBrowserToolEnabled] = useState(true);
  const [remoteBrowserEnabled, setRemoteBrowserEnabled] = useState(false);
  const [remoteBrowserHost, setRemoteBrowserHost] = useState('');

  const [codebaseIndexingButtonDisabled, setCodebaseIndexingButtonDisabled] = useState(false);
  const [customInstructions, setCustomInstructions] = useState('');
  const [projectRuleName, setProjectRuleName] = useState('');
  const [ruleType, setRuleType] = useState('Always');
  const [filePatterns, setFilePatterns] = useState<string[]>([]);
  const [filePatternInput, setFilePatternInput] = useState('');
  const [codeCompletionsMoreContext, setCompletionsMoreContext] = useState(false);
  const [userName, setUserName] = useState('');
  const [tenant, setTenant] = useState('');
  const [isShowAddRule, setIsShowAddRule] = useState(false);
  const [modalTitle, setModalTitle] = useState('新建规则');
  const [isIDE, setIsIDE] = useState(false);
  const [currentTab, setCurrentTab] = useState<string>('');
  const [defaultMcpTab, setDefaultMcpTab] = useState<McpActiveTab>(McpActiveTab.market);
  const [autoApprovalSettings, setAutoApprovalSettings] =
    useState<AutoApprovalSettings>(DEFAULT_AUTO_APPROVAL_SETTINGS);
  const [autoExecute, setAutoExecute] = useState<boolean>(false);
  const [modesInfo, setModesInfo] = useState<ModesInfo>({
    customModes: [],
    defaultModes: [],
  });
  const [isRemoteEnvironment, setIsRemoteEnvironment] = useState(false);
  const [showCodebase, setShowCodebase] = useState(true);
  // 知识库
  const [showLocalPopup, setLocalPopup] = useState(false);
  const [showUrlPopup, setUrlPopup] = useState(false);
  const [showBasePopup, setBasePopup] = useState(false);
  const [knowledgeList, setKnowledgeList] = useState<any[]>([]);
  const [docList, setDocList] = useState<any[]>([]);
  const [docTotal, setDocTotal] = useState<number>(0);
  const [knowledgeBaseInfo, setBaseInfo] = useState(null);
  const [baseUrl, setBaseUrl] = useState('http://joycoder-api-inner.jd.com');
  const [datasetId, setDatasetId] = useState('');
  const styles = {
    track: {
      backgroundColor: '#247FFFFF',
    },
    tracks: {
      background: '#247FFFFF',
      border: `2px solid rgba(255,255,255,1)`,
    },
    rail: {
      background: `rgba(48,48,53,1)`,
    },
  };

  const marks = {
    0.3: '0.3',
    0.6: '0.6',
    0.9: '0.9',
    1.2: '1.2',
    1.5: '1.5',
    2: '2',
    3: '3',
  };

  useEffect(() => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'setting-connect-verify',
        data: {
          msg: 'connected',
        },
      },
    });
  }, []);

  // const closeSetting = () => {
  //   console.log('关闭页面');
  // };

  /**
   * 预测补全延迟
   * @param value - 新的完成延迟值
   */
  const handleCompletionDelayChange = (value: number) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'completionDelay',
          completionDelay: value,
        },
      },
    });
    setCompletionDelay(value);
  };

  /**
   * 处理最大并发文件读取数变更
   * @param value - 新的完成延迟值
   */
  const handleMaxConcurrentFileReadsChange = (value: number | null) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'maxConcurrentFileReads',
          maxConcurrentFileReads: value || 1,
        },
      },
    });
    setMaxConcurrentFileReads(value || 1);
  };
  /**
   * 跨文件感知
   * @param {CheckboxChangeEvent} e - 复选框变化事件。
   */
  const onCompletionsMoreContextChange = (e: CheckboxChangeEvent) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'completionsMoreContext',
          completionsMoreContext: e.target.checked,
        },
      },
    });
    setCompletionsMoreContext(e.target.checked);
  };
  /**
   * 增量代码评审。
   * @param e - 复选框更改事件。
   */
  const handleCommitCodeReview = (e: CheckboxChangeEvent) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'commitCodeReview',
          commitCodeReview: e.target.checked,
        },
      },
    });
    setCommitCodeReview(e.target.checked);
  };

  const handleSilentMode = (e: CheckboxChangeEvent) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'silentMode',
          silentMode: e.target.checked,
        },
      },
    });
    setSilentMode(e.target.checked);
  };

  const handleTodoListChange = (e: CheckboxChangeEvent) => {
    console.log('启用todolist: ' + e.target.checked);
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'isTodoListEnableChange',
          isTodoListEnable: e.target.checked,
        },
      },
    });
    setIsTodoListEnable(e.target.checked);
  };

  /**
   * 处理选择浏览器大小
   * @param value - 浏览器大小
   */
  const handleBrowserViewportSize = (value: string) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-browser',
        data: {
          type: 'setBrowserToolViewportSize',
          browserViewportSize: value,
        },
      },
    });
    setBrowserViewportSize(value);
  };
  /**
   * 开启浏览器工具
   * @param e - 浏览器工具事件。
   */
  const handleBrowserToolEnabled = (e: CheckboxChangeEvent) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-browser',
        data: {
          type: 'setBrowserToolEnabled',
          browserToolEnabled: e.target.checked,
        },
      },
    });
    setBrowserToolEnabled(e.target.checked);
  };
  /**
   * 开启远端浏览器工具
   * @param e - 浏览器工具事件。
   */
  const handleRemoteBrowserEnabledChange = (e: CheckboxChangeEvent) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-browser',
        data: {
          type: 'setRemoteBrowserEnabled',
          remoteBrowserEnabled: e.target.checked,
        },
      },
    });
    setRemoteBrowserEnabled(e.target.checked);
  };
  /**
   * 浏览器Host
   * @param e - 浏览器host input事件。
   */
  const handleRemoteBrowserHostChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-browser',
        data: {
          type: 'setRemoteBrowserHost',
          remoteBrowserHost: e.target.value,
        },
      },
    });
    setRemoteBrowserHost(e.target.value);
  };
  /**
   * 处理errorLIne状态。
   * @param e - 复选框变化事件。
   */
  const handleErrorLine = (e: CheckboxChangeEvent) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'errorLine',
          errorLine: e.target.checked,
        },
      },
    });
    setErrorLine(e.target.checked);
  };

  /**
   * 处理行间菜单
   * @param checkedValues - 选中的复选框值数组
   */
  const onCodeLensChange = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    const value = e.target.value;
    // 使用展开运算符创建 codeLensList 的副本，以避免直接修改状态
    let codeLensList = [...codeLens];
    if (checked) {
      // 使用副本更新数组
      if (!codeLensList.includes(value)) {
        codeLensList.push(value);
      }
    } else {
      // 使用副本更新数组
      codeLensList = codeLensList.filter((item) => item !== value);
    }
    // 发送消息给 vscode
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'codeLens',
          codeLens: codeLensList,
        },
      },
    });
    // 使用 setCodeLens 更新状态
    setCodeLens(codeLensList);
  };

  /**
   * 处理CommitMessage
   * @param value - 提交信息的值
   */
  const handleCommitMessageChange = (value: string) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'commitMessage',
          commitMessage: value,
        },
      },
    });
    setCommitMessage(value);
  };
  /**
   * 处理AI规则配置
   * @param e - 输入框变化事件
   */
  const handleCustomInstructionsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setCustomInstructions(value);
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'customInstructions',
          customInstructions: value,
        },
      },
    });
  };

  /**
   * 处理项目AI规则名称输入
   * @param e - 输入框变化事件
   */
  const handleProjectRuleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setProjectRuleName(e.target.value);
  };

  /**
   * 处理规则类型变更
   * @param value - 选中的规则类型
   */
  const handleRuleTypeChange = (value: string) => {
    setRuleType(value);
  };

  /**
   * 处理文件模式输入变更
   * @param e - 输入框变化事件
   */
  const handleFilePatternInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilePatternInput(e.target.value);
  };

  /**
   * 添加文件模式
   */
  const handleAddFilePattern = () => {
    if (filePatternInput.trim() === '') {
      return;
    }
    setFilePatterns([...filePatterns, filePatternInput.trim()]);
    setFilePatternInput('');
  };

  /**
   * 删除文件模式
   * @param pattern - 要删除的文件模式
   */
  const handleRemoveFilePattern = (pattern: string) => {
    setFilePatterns(filePatterns.filter((p) => p !== pattern));
  };

  /**
   * 处理创建项目AI规则
   */
  const handleCreateProjectRule = () => {
    if (projectRuleName.trim() === '') {
      vscode.postMessage<CommonMessage>({
        type: 'COMMON',
        payload: {
          type: 'chatgpt-setting-info',
          data: {
            type: 'chatgpt-setting-error',
            message: '规则名称不能为空',
          },
        },
      });
      return;
    }

    const ruleContent = `---
globs: ${filePatterns.length > 0 ? filePatterns.join(',') : '*'}
alwaysApply: ${ruleType === 'Always'}
---

# 项目规则要求如下

`;

    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'createProjectRule',
          ruleContent: ruleContent,
          filePath: `.joycode/rules/${projectRuleName.trim()}.mdc`,
        },
      },
    });

    setProjectRuleName('');
    setFilePatterns([]);
    setFilePatternInput('');
    setIsShowAddRule(false);
  };
  /**
   * Codebase索引操作
   */
  const handleCodebaseIndexing = (action: string) => {
    return () => {
      setCodebaseIndexingButtonDisabled(true);
      setTimeout(() => {
        setCodebaseIndexingButtonDisabled(false);
      }, 1000);

      vscode.postMessage<CommonMessage>({
        type: 'COMMON',
        payload: {
          type: 'codebaseIndexing',
          data: {
            type: 'codebaseIndexing',
            action,
          },
        },
      });
    };
  };

  /**
   * 处理文件本地弹窗
   */
  const handleLocalPopup = (params: any) => {
    setLocalPopup(true);
    setDatasetId(params.id);
  };
  /**
   * 处理文件URL弹窗
   */
  const handleUrlPopup = (params: any) => {
    setUrlPopup(true);
    setDatasetId(params.id);
  };
  /**
   * 编辑知识库弹窗
   */
  const editKnowledgeBase = (params?: any) => {
    setBaseInfo(params);
    setBasePopup(true);
  };

  /**
   * 删除知识库
   */
  const delKnowledgeBase = (params: any) => {
    // 使用modal实例的confirm方法，而不是Modal.confirm静态方法
    // 这样可以自动继承ConfigProvider的前缀配置
    modal.confirm({
      title: '确定要删除这条信息吗?',
      icon: <ExclamationCircleOutlined />,
      content: `${params?.name ? params?.name + '，' : ''}删除后不可恢复，请再次确认`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      zIndex: 1050, // 确保足够高的 z-index
      maskClosable: false, // 防止点击蒙层关闭
      okButtonProps: {
        danger: true,
      },
      onOk() {
        vscode.postMessage<CommonMessage>({
          type: 'COMMON',
          payload: {
            type: 'getKnowledgeBaseInfo',
            dataType: 'delete',
            data: { id: params?.id }, // 传递要删除的知识库ID
          },
        });
      },
      onCancel() {},
    });
  };

  const initKnowledgeBase = () => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'getKnowledgeBaseInfo',
        dataType: 'list',
      },
    });
  };
  const handleCollapseChange = (datasetId: string | string[], page?: number) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'getDocInfo',
        dataType: 'list',
        data: {
          page: page || 1,
          page_size: 20,
          datasetId,
        },
      },
    });
  };
  const reChunkDoc = (args: { id: string }, datasetId: string, page: number) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'getDocInfo',
        dataType: 'reChunk',
        data: {
          document_ids: [args.id],
          datasetId,
          page,
        },
      },
    });
  };
  const delDocItem = (
    args: {
      name: string;
      id: string;
    },
    datasetId: string,
    page: number
  ) => {
    modal.confirm({
      title: '确定要删除这个文档吗?',
      icon: <ExclamationCircleOutlined />,
      content: `${args?.name ? args?.name + '，' : ''}删除后不可恢复，请再次确认`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      zIndex: 1050, // 确保足够高的 z-index
      maskClosable: false, // 防止点击蒙层关闭
      okButtonProps: {
        danger: true,
      },
      onOk() {
        vscode.postMessage<CommonMessage>({
          type: 'COMMON',
          payload: {
            type: 'getDocInfo',
            dataType: 'delete',
            data: {
              document_id: args.id,
              datasetId,
              page,
            },
          },
        });
      },
      onCancel() {},
    });
  };
  const stopChunk = (
    args: {
      name: string;
      id: string;
    },
    datasetId: string,
    page: number
  ) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'getDocInfo',
        dataType: 'stopChunk',
        data: {
          document_id: args.id,
          datasetId,
          page,
        },
      },
    });
  };

  const getNormalUrlData = async ({ docUrl, docName, datasetId, filesData, type }: Record<string, string>) => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'getDocInfo',
        dataType: 'normalUrl',
        data: {
          docUrl,
          docName,
          datasetId,
          filesData,
          type,
        },
      },
    });
    // 返回一个Promise，等待消息响应
    return new Promise<any>((resolve, reject) => {
      const handleMessage = (event: MessageEvent<any>) => {
        const message = event.data;
        if (message.type === 'COMMON' && message.payload?.type === 'update-url-data') {
          const urlData = message.payload.data;
          window.removeEventListener('message', handleMessage);
          resolve(urlData);
        }
      };

      window.addEventListener('message', handleMessage);

      // 设置超时处理
      setTimeout(() => {
        window.removeEventListener('message', handleMessage);
        reject(new Error('获取URL数据超时'));
      }, 30000); // 30秒超时
    });
  };

  useEffect(() => {
    handleCodebaseIndexing('getProgress')();
  }, []);

  // 初始化知识库
  useEffect(() => {
    if (tenant === 'JD' && isIDE) {
      initKnowledgeBase();
    }
  }, [tenant, isIDE]);

  /**
   * 处理CommitMessage
   * @param value - 提交信息的值
   */
  const handleMoreSetting = () => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'chatgpt-setting-info',
        data: {
          type: 'moreSetting',
        },
      },
    });
  };

  // 登出
  const handleLogout = () => {
    setUserName('');
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'settiing-logout',
        data: {},
      },
    });
  };

  const handleLogin = () => {
    vscode.postMessage<CommonMessage>({
      type: 'COMMON',
      payload: {
        type: 'settiing-login',
        data: {},
      },
    });
  };

  useHandleMessage(({ type, data }) => {
    switch (type) {
      case 'switch-setting-view':
        console.log('设置参数：', data);
        setUserName(data?.userInfo.userName || data?.userInfo.erp);
        setTenant(data?.userInfo.tenant || '');
        // setIsShow(data.type === 'setting');
        setCompletionDelay(data.completionDelay as number); // 延迟时间
        setCompletionsMoreContext(data.completionsMoreContext as boolean); // 跨文件感知
        setCommitCodeReview(data.commitCodeReview as boolean); // 增量代码评审
        setSilentMode(data.silentMode as boolean); // 后台编辑
        // setCodeReviewImmediate(data.codeReviewImmediate as boolean); // 主动代码评审
        setErrorLine(data.errorLine as boolean); // 展示错误行
        setCodeLens(Array.isArray(data.codeLens) ? data.codeLens : []); // 行间菜单
        setCommitMessage(data.commitMessage as string); //  commitMessage生成类型
        // setWriteAction(data.writeAction as string); // 自动化编程写入模式
        setCustomInstructions((data.customInstructions as string) || ''); // AI规则配置
        setShowCodeLens(data.enableCodeLens as boolean); // 是否展示行间菜单
        setAutoApprovalSettings(data.autoApprovalSettings); // 自动化审批设置
        setAutoExecute(data.autoExecute as boolean); // 自动执行状态
        setIsIDE(true || data.isIDE); // TODO: 是否是IDE
        setBaseUrl(data.baseUrl); // baseURl
        setShowCodebase(data.showCodebase as boolean); // 是否展示Codebase设置
        setHasWorkspaceFolder(data.hasWorkspaceFolder as boolean); //是否有工作目录（无工作目录不能进行Codebase操作）
        setModesInfo(data.modesInfo);
        setIsRemoteEnvironment(data.isRemoteEnvironment); // 是否是远程环境
        setBrowserToolEnabled(data.browserToolEnabled as boolean); // 是否开启浏览器工具
        setBrowserViewportSize(data.browserViewportSize.viewport as string); // 浏览器工具大小
        setRemoteBrowserEnabled(data.remoteBrowserEnabled as boolean); // 是否开启远端浏览器工具
        setRemoteBrowserHost(data.remoteBrowserHost as string); // 远端浏览器host
        setIsTodoListEnable(data.isTodoListEnable as boolean); // 是否启用todolist

        // 设置当前tab
        if (data.cmdData?.type) {
          setCurrentTab(data.cmdData?.type as string);
        }
        // 设置mcp内tab
        if (data.cmdData?.mcpSubtype) {
          setDefaultMcpTab(data.cmdData?.mcpSubtype as McpActiveTab);
        }
        break;
      case 'codebase-update-progress':
        (data.codebaseIndexingProgress as number) >= 0 &&
          setCodebaseIndexingProgress(data.codebaseIndexingProgress as number);
        data.codebaseIndexingStatus && setCodebaseIndexingStatus(data.codebaseIndexingStatus as CodebaseIndexingStatus);
        break;

      case 'codebase-indexing-button-state':
        setCodebaseIndexingButtonDisabled(data.codebaseIndexingButtonDisabled as boolean);
        break;
      case 'knowledge-base-info':
        switch (data.dataType) {
          case 'list': {
            const list = data?.list ?? [];
            setKnowledgeList(list);
            break;
          }
          case 'add':
          case 'update':
          case 'delete': {
            const baseInfo = data?.baseInfo;
            if (baseInfo?.code === 0) {
              const list = data?.list ?? [];
              setKnowledgeList(list);
              setBasePopup(false);
            } else {
              message.error(baseInfo.message ?? `知识库${data.dataType !== 'delete' ? '编辑' : '删除'}失败`);
            }
            break;
          }
        }

        break;
      case 'update-doc-info':
        switch (data.dataType) {
          case 'list': {
            const list = data?.list.docs ?? [];
            setDocList(list);
            setDocTotal(data?.list.total ?? 0);
            break;
          }
        }

        break;
      case 'joyspace-auth-data':
        const code = data?.code;
        if (code === 0) {
          message.success('joyspace 授权成功');
        } else {
          message.error(data?.msg || 'joyspace 授权失败');
        }
        break;
      case 'login-user-info':
        setUserName(data?.userName);
        break;
      default:
        break;
    }
  });

  const tabsEnu: { [key: string]: string } = {
    general: 'general',
    special: 'special',
    agent: 'agent',
    mcp: 'mcp',
    codebase: 'codebase',
    rules: 'rules',
  };

  const settingItems: TabsProps['items'] = [
    {
      key: 'general',
      label: (
        <>
          <i className="icon iconfont icon-tongyongshezhi mr-10"></i>通用
        </>
      ),
      children: (
        <GeneralTab
          userName={userName}
          onLogout={handleLogout}
          onLogin={handleLogin}
          onMoreSetting={handleMoreSetting}
          isIDE={isIDE}
        />
      ),
    },
    {
      key: 'special',
      label: (
        <>
          <i className="icon iconfont icon-texing mr-10"></i>特性
        </>
      ),
      children: (
        <SpecialTab
          completionDelay={completionDelay}
          codeCompletionsMoreContext={codeCompletionsMoreContext}
          isShowCodeLens={isShowCodeLens}
          codeLens={codeLens}
          commitMessage={commitMessage}
          isCommitCodeReview={isCommitCodeReview}
          isSilentMode={isSilentMode}
          isTodoListEnable={isTodoListEnable}
          marks={marks}
          isErrorLine={isErrorLine}
          browserToolEnabled={browserToolEnabled}
          browserViewportSize={browserViewportSize}
          remoteBrowserEnabled={remoteBrowserEnabled}
          maxConcurrentFileReads={maxConcurrentFileReads}
          remoteBrowserHost={remoteBrowserHost}
          styles={styles}
          onCompletionDelayChange={handleCompletionDelayChange}
          onCompletionsMoreContextChange={onCompletionsMoreContextChange}
          onCodeLensChange={onCodeLensChange}
          onCommitMessageChange={handleCommitMessageChange}
          onCommitCodeReviewChange={handleCommitCodeReview}
          onSilentModeChange={handleSilentMode}
          onTodoListChange={handleTodoListChange}
          onErrorLineChange={handleErrorLine}
          onBrowserViewportSizeChange={handleBrowserViewportSize}
          onBrowserToolEnabledChange={handleBrowserToolEnabled}
          onRemoteBrowserEnabledChange={handleRemoteBrowserEnabledChange}
          onRemoteBrowserHostChange={handleRemoteBrowserHostChange}
          onMaxConcurrentFileReadsChange={handleMaxConcurrentFileReadsChange}
        />
      ),
    },
    {
      key: 'agent',
      label: (
        <>
          <i className="icon iconfont icon-zhinengti mr-10"></i>智能体
        </>
      ),
      children: (
        <AgentManagement
          autoApprovalSettings={autoApprovalSettings}
          autoExecute={autoExecute}
          modesInfo={modesInfo}
          isRemoteEnvironment={isRemoteEnvironment}
          setAutoApprovalSettings={setAutoApprovalSettings}
        />
      ),
    },
    {
      key: 'mcp',
      label: (
        <>
          <i className="icon iconfont icon-mcp mr-10"></i>MCP
        </>
      ),
      children: <McpMarket defaultMcpTab={defaultMcpTab} />,
    },
    {
      key: 'codebase',
      label: (
        <>
          <i className="icon iconfont icon-shangxiawen mr-10"></i>上下文
        </>
      ),
      children: (
        <CodebaseTab
          hasWorkspaceFolder={hasWorkspaceFolder}
          codebaseIndexingProgress={codebaseIndexingProgress}
          codebaseIndexingStatus={codebaseIndexingStatus}
          codebaseIndexingButtonDisabled={codebaseIndexingButtonDisabled}
          onCodebaseIndexing={handleCodebaseIndexing}
          isRemoteEnvironment={isRemoteEnvironment}
          openLocalPopup={handleLocalPopup}
          openUrlPopup={handleUrlPopup}
          editKnowledgeBase={editKnowledgeBase}
          delKnowledgeBase={delKnowledgeBase}
          knowledgeBaseList={knowledgeList}
          docList={docList}
          docTotal={docTotal}
          onCollapseChange={handleCollapseChange}
          isIDE={isIDE}
          tenant={tenant}
          reChunkDoc={reChunkDoc}
          delDocItem={delDocItem}
          stopChunk={stopChunk}
        />
      ),
      // disabled: !showCodebase,
    },
    {
      key: 'rules',
      label: (
        <>
          <i className="icon iconfont icon-guize mr-10"></i>规则
        </>
      ),
      children: (
        <RulesTab
          customInstructions={customInstructions}
          isShowAddRule={isShowAddRule}
          modalTitle={modalTitle}
          projectRuleName={projectRuleName}
          ruleType={ruleType}
          filePatterns={filePatterns}
          filePatternInput={filePatternInput}
          onCustomInstructionsChange={handleCustomInstructionsChange}
          onShowAddRule={setIsShowAddRule}
          onProjectRuleNameChange={handleProjectRuleNameChange}
          onRuleTypeChange={handleRuleTypeChange}
          onFilePatternInputChange={handleFilePatternInputChange}
          onAddFilePattern={handleAddFilePattern}
          onRemoveFilePattern={handleRemoveFilePattern}
          onCreateProjectRule={handleCreateProjectRule}
        />
      ),
    },
  ];

  if (!showCodebase) {
    settingItems.splice(4, 1);
  }

  return (
    <div className="joycoder-setting-content">
      {/* 渲染Modal的上下文持有者，确保modal实例可以访问ConfigProvider的配置 */}
      {contextHolder}
      <div className="setting-title">JoyCode 设置</div>
      <Tabs
        activeKey={tabsEnu[currentTab] || 'general'}
        onChange={(key) => {
          setCurrentTab(Object.keys(tabsEnu).find((k) => tabsEnu[k] === key) || '');
        }}
        items={settingItems}
        tabPosition="left"
        size="small"
        tabBarGutter={0}
        className="setting-tabs"
      />
      {isIDE && tenant === 'JD' && (
        <>
          <LoaclPopup
            open={showLocalPopup}
            getNormalUrlData={getNormalUrlData}
            setLocalPopup={setLocalPopup}
            datasetId={datasetId}
            title="编辑文档"
          />
          <URLPopup
            open={showUrlPopup}
            getNormalUrlData={getNormalUrlData}
            setUrlPopup={setUrlPopup}
            datasetId={datasetId}
            title="编辑文档"
          />
          <BasePopup
            knowledgeBaseInfo={knowledgeBaseInfo}
            open={showBasePopup}
            setBasePopup={setBasePopup}
            title="编辑知识库"
          />
        </>
      )}
    </div>
  );
}
