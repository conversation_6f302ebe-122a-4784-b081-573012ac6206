// 库
import React, { Component } from 'react';
// 业务组件
import ToolbarInput from './toolbar-input';
import ToolbarDeviceSetting from './toolbar-device-setting';
// 业务定义
import { IHistoryBtnStatus, IEmulatedDeviceData, IViewportMetadata } from '../../shared/interface';
// 样式
import './toolbar.scss';
// 本地定义
type IToolbarProps = {
  url: string;
  historyBtnStatus: IHistoryBtnStatus;
  viewportMetadata: IViewportMetadata;
  onActionInvoked: (type: string, data?: Record<string, any>) => void;
};

/**
 * 页面操作工具栏
 */
export default class Toolbar extends Component<IToolbarProps, unknown> {
  public render() {
    const { historyBtnStatus, viewportMetadata } = this.props;

    return (
      <div className="toolbar">
        <div className="toolbar-inner">
          <button
            onClick={() => {
              this.props.onActionInvoked('backward');
            }}
            title="后退"
            disabled={historyBtnStatus.canGoBack}
          >
            <svg width="1em" height="1em" viewBox="0 0 32 32">
              <path d="M14 26l1.41-1.41L7.83 17H28v-2H7.83l7.58-7.59L14 6L4 16l10 10z" fill="currentColor"></path>
            </svg>
          </button>
          <button
            onClick={() => {
              this.props.onActionInvoked('forward');
            }}
            title="前进"
            disabled={historyBtnStatus.canGoForward}
          >
            <svg width="1em" height="1em" viewBox="0 0 32 32">
              <path d="M18 6l-1.43 1.393L24.15 15H4v2h20.15l-7.58 7.573L18 26l10-10L18 6z" fill="currentColor"></path>
            </svg>
          </button>
          <button
            onClick={() => {
              this.props.onActionInvoked('refresh');
            }}
            title="刷新"
          >
            <svg width="1em" height="1em" viewBox="0 0 32 32">
              <path d="M12 10H6.78A11 11 0 0 1 27 16h2A13 13 0 0 0 6 7.68V4H4v8h8z" fill="currentColor"></path>
              <path d="M20 22h5.22A11 11 0 0 1 5 16H3a13 13 0 0 0 23 8.32V28h2v-8h-8z" fill="currentColor"></path>
            </svg>
          </button>
          <ToolbarInput
            url={this.props.url}
            onUrlChanged={(url: string) => {
              this.props.onActionInvoked('urlChange', { url });
            }}
          />
          {/* <button
            className={viewportMetadata.isEnabledDevice ? 'active' : ''}
            onClick={() => {
              this.props.onActionInvoked('enabledDevice');
            }}
            title="切换设备"
          >
            <svg width="0.95em" height="0.95em" viewBox="0 0 32 32">
              <path
                d="M10 30H4a2 2 0 0 1-2-2V16a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2zM4 16v12h6V16z"
                fill="currentColor"
              ></path>
              <path
                d="M28 4H6a2 2 0 0 0-2 2v6h2V6h22v14H14v2h2v4h-2v2h9v-2h-5v-4h10a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2z"
                fill="currentColor"
              ></path>
            </svg>
          </button> */}
          {/* <button
            onClick={() => {
              this.props.onActionInvoked('createDebugPanel');
            }}
            title="打开调试"
          >
            <svg width="0.9em" height="0.9em" viewBox="0 0 32 32">
              <path
                fill="currentColor"
                d="M 29 18 L 25.988281 18 L 25.988281 11.417969 L 28.703125 8.710938 C 29.09375 8.320312 29.09375 7.6875 28.707031 7.296875 C 28.3125 6.902344 27.679688 6.90625 27.289062 7.292969 L 24.578125 10 L 7.414062 10 L 4.695312 7.292969 C 4.308594 6.902344 3.671875 6.902344 3.285156 7.296875 C 2.894531 7.6875 2.894531 8.320312 3.285156 8.710938 L 6.011719 11.425781 L 6.011719 18 L 3 18 C 2.445312 18 2 18.445312 2 19 C 2 19.554688 2.445312 20 3 20 L 6.011719 20 L 6.011719 20.925781 C 6.003906 20.972656 6 21.019531 6 21.066406 C 6 22.636719 6.421875 24.113281 7.171875 25.410156 L 4.296875 28.289062 C 3.90625 28.679688 3.90625 29.3125 4.296875 29.703125 C 4.492188 29.898438 4.746094 29.996094 5.003906 29.996094 C 5.261719 29.996094 5.515625 29.898438 5.710938 29.703125 L 8.371094 27.039062 C 10.207031 29.042969 12.941406 30.320312 16 30.320312 C 19.058594 30.320312 21.796875 29.042969 23.628906 27.039062 L 26.292969 29.707031 C 26.488281 29.902344 26.742188 30 27 30 C 27.257812 30 27.511719 29.902344 27.707031 29.707031 C 28.097656 29.316406 28.097656 28.683594 27.707031 28.292969 L 24.828125 25.410156 C 25.574219 24.113281 25.996094 22.636719 25.996094 21.066406 C 25.996094 21.019531 25.992188 20.972656 25.988281 20.925781 L 25.988281 20 L 29 20 C 29.554688 20 30 19.554688 30 19 C 30 18.445312 29.554688 18 29 18 Z M 16.988281 28.265625 C 16.996094 28.214844 17 28.160156 17 28.101562 L 17 17 C 17 16.445312 16.554688 16 16 16 C 15.445312 16 15 16.445312 15 17 L 15 28.105469 C 15 28.164062 15.003906 28.214844 15.011719 28.269531 C 11.113281 27.832031 8.074219 24.839844 8 21.203125 C 8.007812 21.160156 8.011719 21.117188 8.011719 21.074219 L 8.011719 12 L 23.988281 12 L 23.988281 21.070312 C 23.988281 21.113281 23.992188 21.15625 23.996094 21.199219 C 23.925781 24.835938 20.886719 27.828125 16.988281 28.265625 Z M 16.988281 28.265625 "
              />
              <path
                fill="currentColor"
                d="M 11 8 L 21 8 C 21.320312 8 21.617188 7.851562 21.808594 7.59375 C 21.992188 7.335938 22.050781 7.007812 21.957031 6.703125 C 21.078125 3.890625 18.679688 2 16 2 C 13.316406 2 10.921875 3.890625 10.046875 6.703125 C 9.949219 7.007812 10.007812 7.335938 10.195312 7.59375 C 10.382812 7.851562 10.679688 8 11 8 Z M 16 4 C 17.371094 4 18.640625 4.765625 19.441406 6 L 12.558594 6 C 13.359375 4.765625 14.628906 4 16 4 Z M 16 4 "
              />
            </svg>
          </button> */}
          <button
            onClick={() => {
              this.props.onActionInvoked('openWithBrowse');
            }}
            title="用系统浏览器打开"
          >
            <svg width="0.75em" height="0.75em" viewBox="0 0 32 32">
              <path
                fill="currentColor"
                d="M 28 28 L 4 28 L 4 4 L 12 4 L 12 2 L 2 2 L 2 30 L 30 30 L 30 20 L 28 20 Z M 28 28 "
              />
              <path
                fill="currentColor"
                d="M 20 2 L 20 4 L 26.601562 4 L 10.800781 19.800781 L 12.199219 21.199219 L 28 5.398438 L 28 12 L 30 12 L 30 2 Z M 20 2 "
              />
            </svg>
          </button>
          {/* <button
            onClick={() => {
              this.props.onActionInvoked('setting');
            }}
            title="设置"
          >
            <svg width="1.05em" height="1.05em" viewBox="0 0 32 32">
              <path
                fill="currentColor"
                d="M 28.5625 13.496094 C 28.410156 12.6875 27.914062 12.167969 27.28125 12.167969 L 27.171875 12.167969 C 25.460938 12.167969 24.070312 10.777344 24.070312 9.066406 C 24.070312 8.527344 24.328125 7.910156 24.339844 7.882812 C 24.65625 7.175781 24.414062 6.304688 23.773438 5.859375 L 20.566406 4.070312 L 20.519531 4.046875 C 19.875 3.769531 18.996094 3.949219 18.519531 4.445312 C 18.171875 4.804688 16.976562 5.824219 16.066406 5.824219 C 15.140625 5.824219 13.945312 4.785156 13.597656 4.417969 C 13.121094 3.917969 12.25 3.726562 11.601562 4.007812 L 8.277344 5.828125 L 8.226562 5.859375 C 7.589844 6.304688 7.34375 7.171875 7.660156 7.882812 C 7.667969 7.90625 7.929688 8.519531 7.929688 9.066406 C 7.929688 10.777344 6.539062 12.167969 4.828125 12.167969 L 4.699219 12.167969 C 4.085938 12.167969 3.589844 12.6875 3.4375 13.496094 C 3.429688 13.550781 3.171875 14.914062 3.171875 16.007812 C 3.171875 17.101562 3.429688 18.464844 3.4375 18.519531 C 3.589844 19.324219 4.085938 19.847656 4.71875 19.847656 L 4.828125 19.847656 C 6.539062 19.847656 7.929688 21.238281 7.929688 22.949219 C 7.929688 23.492188 7.671875 24.105469 7.660156 24.128906 C 7.34375 24.839844 7.585938 25.710938 8.222656 26.15625 L 11.371094 27.921875 L 11.417969 27.941406 C 12.070312 28.230469 12.953125 28.042969 13.425781 27.523438 C 13.867188 27.046875 15.058594 26.058594 15.933594 26.058594 C 16.882812 26.058594 18.105469 27.164062 18.457031 27.554688 C 18.78125 27.910156 19.28125 28.121094 19.789062 28.121094 C 20.027344 28.121094 20.253906 28.074219 20.460938 27.988281 L 23.726562 26.1875 L 23.773438 26.15625 C 24.414062 25.710938 24.65625 24.839844 24.339844 24.132812 C 24.332031 24.105469 24.070312 23.496094 24.070312 22.949219 C 24.070312 21.238281 25.460938 19.847656 27.171875 19.847656 L 27.300781 19.847656 C 27.914062 19.847656 28.410156 19.324219 28.5625 18.519531 C 28.570312 18.464844 28.828125 17.101562 28.828125 16.007812 C 28.828125 14.914062 28.570312 13.550781 28.5625 13.496094 M 26.96875 16.007812 C 26.96875 16.714844 26.828125 17.621094 26.761719 18 C 24.199219 18.210938 22.207031 20.355469 22.207031 22.949219 C 22.207031 23.679688 22.441406 24.382812 22.5625 24.703125 L 19.78125 26.242188 C 19.640625 26.097656 19.238281 25.691406 18.6875 25.28125 C 17.722656 24.5625 16.792969 24.199219 15.933594 24.199219 C 15.082031 24.199219 14.164062 24.554688 13.199219 25.261719 C 12.652344 25.660156 12.253906 26.058594 12.113281 26.207031 L 9.433594 24.707031 C 9.566406 24.367188 9.792969 23.675781 9.792969 22.949219 C 9.792969 20.355469 7.800781 18.210938 5.238281 18 C 5.171875 17.621094 5.03125 16.714844 5.03125 16.007812 C 5.03125 15.300781 5.171875 14.390625 5.238281 14.011719 C 7.800781 13.804688 9.792969 11.660156 9.792969 9.066406 C 9.792969 8.339844 9.558594 7.636719 9.4375 7.3125 L 12.289062 5.746094 C 12.414062 5.867188 12.820312 6.265625 13.378906 6.664062 C 14.328125 7.339844 15.230469 7.683594 16.066406 7.683594 C 16.894531 7.683594 17.789062 7.347656 18.734375 6.683594 C 19.292969 6.289062 19.699219 5.894531 19.820312 5.785156 L 22.566406 7.308594 C 22.441406 7.628906 22.207031 8.332031 22.207031 9.066406 C 22.207031 11.660156 24.199219 13.804688 26.761719 14.011719 C 26.828125 14.394531 26.96875 15.304688 26.96875 16.007812 "
              />
              <path
                fill="currentColor"
                d="M 15.945312 11.425781 C 13.425781 11.425781 11.378906 13.472656 11.378906 15.992188 C 11.378906 18.507812 13.425781 20.554688 15.945312 20.554688 C 18.460938 20.554688 20.507812 18.507812 20.507812 15.992188 C 20.507812 13.472656 18.460938 11.425781 15.945312 11.425781 M 18.648438 15.992188 C 18.648438 17.480469 17.433594 18.691406 15.945312 18.691406 C 14.453125 18.691406 13.242188 17.480469 13.242188 15.992188 C 13.242188 14.5 14.453125 13.289062 15.945312 13.289062 C 17.433594 13.289062 18.648438 14.5 18.648438 15.992188 "
              />
            </svg>
          </button> */}
        </div>
        <ToolbarDeviceSetting
          viewportMetadata={viewportMetadata}
          onSizeChange={(size: Record<string, number>) => {
            this.props.onActionInvoked('resize', size);
          }}
          onDeviceChange={(selectedDevice: IEmulatedDeviceData) => {
            this.props.onActionInvoked('changeDevice', selectedDevice);
          }}
        ></ToolbarDeviceSetting>
      </div>
    );
  }
}
