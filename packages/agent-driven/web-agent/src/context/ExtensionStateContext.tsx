import React, { createContext, useCallback, useContext, useEffect, useState, useMemo } from 'react';
import { useEvent } from 'react-use';
import { DEFAULT_AUTO_APPROVAL_SETTINGS } from '../../../src/shared/AutoApprovalSettings';
import { ExtensionMessage, ExtensionState, DEFAULT_PLATFORM } from '../../../src/shared/ExtensionMessage';
import {
  ApiConfiguration,
  ModelInfo,
  openRouterDefaultModelId,
  openRouterDefaultModelInfo,
} from '../../../src/shared/api';
import { findLastIndex } from '../../../src/shared/array';
import { McpServer } from '@joycoder/shared/src/mcp/mcp';
import { convertTextMateToHljs } from '../utils/textMateToHljs';
import { vscode } from '../utils/vscode';
import { DEFAULT_BROWSER_SETTINGS } from '../../../src/shared/BrowserSettings';
import { defaultModeSlug, defaultPrompts } from '../utils/modes';
import { CustomSupportPrompts } from '../utils/support-prompt';

interface ExtensionStateContextType extends ExtensionState {
  didHydrateState: boolean;
  showWelcome: boolean;
  theme: any;
  promptList: any;
  docList: any;
  openRouterModels: Record<string, ModelInfo>;
  openAiModels: string[];
  mcpServers: McpServer[];
  filePaths: string[];
  ruleFiles: string[]; // 添加 ruleFiles 属性
  resourceFiles: string[]; // 添加 resourceFiles 属性
  totalTasksSize: number | null;
  thinkingMode: boolean; // 添加思考模式状态
  webSearchEnabled: boolean; // 添加联网搜索状态
  autoExecute: boolean; // 添加自动执行状态
  setApiConfiguration: (config: ApiConfiguration) => void;
  setCustomInstructions: (value?: string) => void;
  setShowAnnouncement: (value: boolean) => void;
  customModes: any[];
  setCustomModes: (value: any[]) => void;
  mode: any;
  setMode: (value: any) => void;
  setCustomModePrompts: (value: any) => void;
  setCustomSupportPrompts: (value: CustomSupportPrompts) => void;
  setThinkingMode: (value: boolean) => void; // 添加设置思考模式的方法
  setWebSearchEnabled: (value: boolean) => void; // 添加设置联网搜索的方法
  setAutoExecute: (value: boolean) => void; // 添加设置自动执行的方法
}

const ExtensionStateContext = createContext<ExtensionStateContextType | undefined>(undefined);

export const ExtensionStateContextProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [state, setState] = useState<ExtensionState>({
    version: '',
    joycoderMessages: [],
    taskHistory: [],
    shouldShowAnnouncement: false,
    autoApprovalSettings: DEFAULT_AUTO_APPROVAL_SETTINGS,
    browserSettings: DEFAULT_BROWSER_SETTINGS,
    isLoggedIn: false,
    platform: DEFAULT_PLATFORM,
    isRemoteEnvironment: false,
    updateStatus: 'done',
    mode: defaultModeSlug,
    customSupportPrompts: {},
    customModePrompts: defaultPrompts,
    customModes: [],
    cwd: '',
    isIDE: false,
    userInfo: {},
  });
  const [didHydrateState, setDidHydrateState] = useState(false);
  const [showWelcome, setShowWelcome] = useState(false);
  const [theme, setTheme] = useState<any>(undefined);
  const [promptList, setPromptList] = useState<any[]>([]);
  const [updateStatus, setUpdateStatus] = useState<string>('done');
  const [filePaths, setFilePaths] = useState<string[]>([]);
  const [ruleFiles, setRuleFiles] = useState<string[]>([]); // 添加 ruleFiles 状态
  const [resourceFiles, setResourceFiles] = useState<string[]>([]); // 添加 资源 状态
  const [openRouterModels, setOpenRouterModels] = useState<Record<string, ModelInfo>>({
    [openRouterDefaultModelId]: openRouterDefaultModelInfo,
  });
  const [totalTasksSize, setTotalTasksSize] = useState<number | null>(null);
  const [openAiModels, setOpenAiModels] = useState<string[]>([]);
  const [mcpServers, setMcpServers] = useState<McpServer[]>([]);
  const [docList, setDocList] = useState<any[]>([]);
  const [thinkingMode, setThinkingModeState] = useState<boolean>(false);
  const [webSearchEnabled, setWebSearchEnabledState] = useState<boolean>(false);
  const [autoExecute, setAutoExecuteState] = useState<boolean>(false);

  const handleMessage = useCallback((event: MessageEvent) => {
    const message: ExtensionMessage = event.data;
    switch (message.type) {
      case 'state': {
        setState(message.state!);
        setShowWelcome(false);
        setDidHydrateState(true);
        break;
      }
      case 'modeUpdate': {
        // Only update mode-related state to prevent unnecessary re-renders
        setState((prevState) => ({
          ...prevState,
          mode: message.mode!,
        }));
        break;
      }
      case 'theme': {
        if (message.text) {
          setTheme(convertTextMateToHljs(JSON.parse(message.text)));
        }
        break;
      }
      case 'updateUserPrompt': {
        const promptList = message?.promptList || [];
        setPromptList(promptList);
        break;
      }
      case 'workspaceUpdated': {
        setFilePaths(message.filePaths ?? []);
        setRuleFiles(message.ruleFiles ?? []); // 设置 ruleFiles 状态
        setResourceFiles(message.resourceFiles ?? []); // 设置 ruleFiles 状态
        setUpdateStatus(message.updateStatus ?? 'done');
        break;
      }
      case 'workspaceUpdating': {
        setUpdateStatus(message.updateStatus ?? 'done');
        break;
      }
      case 'docListUpdated':
        setDocList(message.docList ?? []);
        break;
      case 'partialMessage': {
        const partialMessage = message.partialMessage!;
        setState((prevState) => {
          // worth noting it will never be possible for a more up-to-date message to be sent here or in normal messages post since the presentAssistantContent function uses lock
          const lastIndex = findLastIndex(prevState.joycoderMessages, (msg) => msg.ts === partialMessage.ts);
          if (lastIndex !== -1) {
            const newJoyCoderMessages = [...prevState.joycoderMessages];
            newJoyCoderMessages[lastIndex] = partialMessage;
            return { ...prevState, joycoderMessages: newJoyCoderMessages };
          }
          return prevState;
        });
        break;
      }
      case 'openRouterModels': {
        const updatedModels = message.openRouterModels ?? {};
        setOpenRouterModels({
          [openRouterDefaultModelId]: openRouterDefaultModelInfo, // in case the extension sent a model list without the default model
          ...updatedModels,
        });
        break;
      }
      case 'openAiModels': {
        const updatedModels = message.openAiModels ?? [];
        setOpenAiModels(updatedModels);
        break;
      }
      case 'mcpServers': {
        setMcpServers(message.mcpServers ?? []);
        break;
      }
      case 'totalTasksSize': {
        setTotalTasksSize(message.totalTasksSize ?? null);
        break;
      }
      case 'thinkingModeState': {
        console.log('接收到思考模式状态更新:', message.value);
        // 直接更新状态，但只在初始化时
        setThinkingModeState(message.value ?? false);
        break;
      }
      case 'webSearchEnabledState': {
        console.log('接收到联网搜索状态更新:', message.value);
        // 直接更新状态，但只在初始化时
        setWebSearchEnabledState(message.value ?? false);
        break;
      }
      case 'autoExecuteState': {
        console.log('接收到自动执行状态更新:', message.value);
        // 直接更新状态，但只在初始化时
        setAutoExecuteState(message.value ?? false);
        break;
      }
    }
  }, []);

  useEvent('message', handleMessage);

  useEffect(() => {
    vscode.postMessage({ type: 'webviewDidLaunch' });
    // 初始化时获取思考模式状态
    vscode.postMessage({ type: 'getThinkingMode' });
    // 初始化时获取联网搜索状态
    vscode.postMessage({ type: 'getWebSearchEnabled' });
    // 初始化时获取自动执行状态
    vscode.postMessage({ type: 'getAutoExecute' });
  }, []);

  const setThinkingModeCallback = useCallback(
    (value: boolean) => {
      console.log('设置思考模式:', value, '当前状态:', thinkingMode);
      // 防止重复设置相同的值
      if (value === thinkingMode) {
        console.log('状态相同，跳过更新');
        return;
      }
      // 立即更新本地状态，同时发送到后端
      setThinkingModeState(value);
      vscode.postMessage({
        type: 'setThinkingMode',
        value: value,
      });
    },
    [thinkingMode]
  );

  const setWebSearchEnabledCallback = useCallback(
    (value: boolean) => {
      console.log('设置联网搜索:', value, '当前状态:', webSearchEnabled);
      // 防止重复设置相同的值
      if (value === webSearchEnabled) {
        console.log('状态相同，跳过更新');
        return;
      }
      // 立即更新本地状态，同时发送到后端
      setWebSearchEnabledState(value);
      vscode.postMessage({
        type: 'setWebSearchEnabled',
        value: value,
      });
    },
    [webSearchEnabled]
  );

  const setAutoExecuteCallback = useCallback(
    (value: boolean) => {
      console.log('设置自动执行:', value, '当前状态:', autoExecute);
      // 防止重复设置相同的值
      if (value === autoExecute) {
        console.log('状态相同，跳过更新');
        return;
      }
      // 立即更新本地状态，同时发送到后端
      setAutoExecuteState(value);
      vscode.postMessage({
        type: 'setAutoExecute',
        value: value,
      });
    },
    [autoExecute]
  );

  const contextValue: ExtensionStateContextType = useMemo(
    () => ({
      ...state,
      didHydrateState,
      showWelcome,
      theme,
      promptList,
      docList,
      openRouterModels,
      openAiModels,
      mcpServers,
      filePaths,
      ruleFiles, // 在 contextValue 中添加 ruleFiles
      resourceFiles, // 在 contextValue 中添加 resourceFiles
      totalTasksSize,
      thinkingMode,
      webSearchEnabled,
      autoExecute,
      setApiConfiguration: (value) =>
        setState((prevState) => ({
          ...prevState,
          apiConfiguration: value,
        })),
      setCustomInstructions: (value) =>
        setState((prevState) => ({
          ...prevState,
          customInstructions: value,
        })),
      setShowAnnouncement: (value) =>
        setState((prevState) => ({
          ...prevState,
          shouldShowAnnouncement: value,
        })),
      updateStatus,
      setCustomModes: function (value: any[]): void {
        setState((prevState) => ({ ...prevState, customModes: value }));
      },
      setMode: function (value: string): void {
        setState((prevState) => ({ ...prevState, mode: value }));
      },
      setCustomModePrompts: function (value: any): void {
        setState((prevState) => ({ ...prevState, customModePrompts: value }));
      },
      setCustomSupportPrompts: function (value: CustomSupportPrompts): void {
        setState((prevState) => ({
          ...prevState,
          customSupportPrompts: value,
        }));
      },
      setThinkingMode: setThinkingModeCallback,
      setWebSearchEnabled: setWebSearchEnabledCallback,
      setAutoExecute: setAutoExecuteCallback,
    }),
    [
      state,
      didHydrateState,
      showWelcome,
      theme,
      promptList,
      docList,
      openRouterModels,
      openAiModels,
      mcpServers,
      filePaths,
      ruleFiles,
      resourceFiles,
      totalTasksSize,
      thinkingMode,
      webSearchEnabled,
      autoExecute,
      updateStatus,
      setThinkingModeCallback,
      setWebSearchEnabledCallback,
      setAutoExecuteCallback,
    ]
  );

  return <ExtensionStateContext.Provider value={contextValue}>{children}</ExtensionStateContext.Provider>;
};

export const useExtensionState = () => {
  const context = useContext(ExtensionStateContext);
  if (context === undefined) {
    throw new Error('useExtensionState must be used within an ExtensionStateContextProvider');
  }
  return context;
};
