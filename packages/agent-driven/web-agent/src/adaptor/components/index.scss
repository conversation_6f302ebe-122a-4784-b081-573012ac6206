.joycoder-coder-mode-dropdown {
  width: 160px !important;
}
.joycoder-chatgpt-input-icons{
  z-index: 20 !important;
  color: var(--vscode-button-secondaryForeground, #72747C);
  background-color: var(--vscode-button-secondaryBackground, #72747C);
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.joycoder-coder-mode-dropdown-option {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 13px;
  font-family: PingFang SC;
  font-weight: normal;
  color: var(--vscode-editor-foreground);
  line-height: 28px;
  padding: 0 8px;

  >  i {
    display: inline-flex !important;
    font-size: 16px !important;
    vertical-align: text-bottom;
    margin-right: 8px;
  }

  &-title {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  &-action {
    // 特殊操作项样式
  }
}
.joycoder-chatgpt-model-dropdown {
  width: 250px !important;
}
.joycoder-coder-mode-dropdown-button > span {
  font-size: 11px;
  margin-left: 4px;
  overflow: hidden;
  vertical-align: middle;
  display: inline-block;
  color: var(--vscode-button-secondaryForeground, #72747C);
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.joycoder-chatgpt-model-dropdown-button > span {
  font-size: 11px;
  margin-left: 4px;
  overflow: hidden;
  vertical-align: middle;
  display: inline-block;
  color: var(--vscode-button-secondaryForeground, #72747C);
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.joycoder-chatgpt-model-dropdown-button:hover{
  opacity: 0.8;
}
.joycoder-coder-mode-dropdown-button:hover{
  opacity: 0.8;
}

::selection {
  background-color: #303035FF !important;
  color: white !important;
}


.joycoder-parent-task{
  overflow: hidden;
  position: relative;
  justify-content: flex-start;
  align-items: center;
  display: flex;
  padding: 0 12px;
  border-top: 1px solid var(--vscode-dropdown-border);
  border-bottom: 1px solid var(--vscode-dropdown-border);
  &-header {
    display: flex;
    padding: 0px 4px;
    background-color: transparent;
    font-size: 14px;
    color: var(--vscode-sideBarSectionHeader-foreground);
    text-align: left;
    overflow: hidden;
    height: 28px;
    line-height: 28px;
    width: 100%;
  }
}

// 工具条样式
.joycoder-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 20px 0;

  &-left {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  &-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  &-switch-group {
    display: flex;
    align-items: center;
    gap: 8px;

    span {
      font-size: 12px;
      color: var(--vscode-foreground);
      font-weight: 500;
      white-space: nowrap;
    }

    .ant-switch {
      min-width: 28px;
      height: 16px;
      box-shadow: none !important;

      &.ant-switch-checked {
        background-color: rgba(36,127,255,0.3) !important;
        box-shadow: none !important;

        .ant-switch-inner {
          color: rgba(90,168,255,1) !important;
        }
      }
      &.ant-switch-checked:focus{
        box-shadow: none !important;
      }
      &.ant-switch-checked:focus:hover{
        box-shadow: none !important;
      }

      &:not(.ant-switch-checked) {
        background-color: var(--vscode-button-secondaryBackground) !important;
      }

      .ant-switch-inner {
        color: var(--vscode-button-secondaryForeground, #72747C);
      }
    }
    .ant-switch:focus{
      box-shadow: none !important;
    }
    .ant-switch:focus:hover{
      box-shadow: none !important;
    }
  }

  &-icon-group {
    display: flex;
    align-items: center;
    gap: 9px;

    .icon {
      font-size: 12px;
      color: var(--vscode-button-secondaryForeground, #72747C);
      background-color: var(--vscode-button-secondaryBackground, #72747C);
      cursor: pointer;
      padding: 2px 4px;
      border-radius: 4px;
      transition: all 0.2s ease;
      opacity: 0.5;

      &:hover {
        opacity: 1;
      }
    }
  }

  &-settings-icon {   
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 9px;

    .icon {
      font-size: 12px;
      padding: 2px 4px;
      border-radius: 4px;
      transition: all 0.2s ease;
      color: var(--vscode-button-secondaryForeground, #72747C);
      background-color: var(--vscode-button-secondaryBackground, #72747C);
      cursor: pointer; 
      opacity: 0.5;

      &:hover {
        opacity: 1;
      }
    }
  }

  // 新会话按钮样式优化
  .ant-btn {
    height: 28px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 6px;
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--vscode-button-secondaryHoverBackground) !important;
      border-color: var(--vscode-button-secondaryHoverBackground) !important;
      color: var(--vscode-button-secondaryForeground) !important;
    }
  }
}

// 快捷工具设置弹框样式
.joycoder-quick-tools-settings {
  position: absolute;
  bottom: 100%;
  left: -20px;
  margin-bottom: 8px;
  z-index: 1000;
  background-color: var(--vscode-dropdown-background);
  border: 1px solid var(--vscode-dropdown-border);
  border-radius: 8px;
  padding: 16px 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  opacity: 1;

  // 添加小箭头指向设置按钮
  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 23px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid var(--vscode-dropdown-background);
  }

  &::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 23px;
    margin-top: 1px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid var(--vscode-dropdown-border);
  }

  &-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 16px;
    color: var(--vscode-foreground);
    text-align: left;
    border-bottom: 1px solid var(--vscode-foreground);
    padding: 0 0 8px 8px;
    opacity: 0.5;
  }

  &-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding-left: 8px;
  }

  &-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 0;

    span {
      font-size: 13px;
      color: var(--vscode-foreground);
      font-weight: 500;
    }

    .ant-switch {
      min-width: 28px;
      height: 16px;

      &.ant-switch-checked {
        background-color: var(--vscode-button-background) !important;
      }
      &.ant-switch-checked:focus{
        box-shadow: none !important;
      }
      &.ant-switch-checked:focus:hover{
        box-shadow: none !important;
      }

      &:not(.ant-switch-checked) {
        background-color: rgba(255, 255, 255, 0.3) !important;
      }
    }
    .ant-switch:focus{
      box-shadow: none !important;
    }
    .ant-switch:focus:hover{
      box-shadow: none !important;
    }
  }
}
