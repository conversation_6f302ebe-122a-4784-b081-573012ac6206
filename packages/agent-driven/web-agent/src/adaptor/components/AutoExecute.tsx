import { Switch } from 'antd';
import { useExtensionState } from "../../context/ExtensionStateContext";

//  自动执行组件
const AutoExecuteTool = () => {
    const { autoExecute, setAutoExecute } = useExtensionState();

    return (
        <>
            <div className="joycoder-toolbar-switch-group">
                <Switch
                    size="small"
                    checkedChildren="Auto"
                    unCheckedChildren="Auto"
                    checked={autoExecute}
                    onChange={setAutoExecute}
                />
            </div>
        </>
    )
}

export default AutoExecuteTool;
