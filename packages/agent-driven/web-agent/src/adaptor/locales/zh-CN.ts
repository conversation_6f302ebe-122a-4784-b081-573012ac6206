import { JOYCODE_BRAND_NAME } from '@joycoder/shared/src/constants/uiConstants';

const placeholder = '通过 "@" 添加上下文，"/"使用快捷指令，“↑↓”切换历史输入，“⇧+ ↩ ”换行';

export const zhCN = {
  common: {
    approve: '同意',
    reject: '拒绝',
    save: '保存',
    cancel: '取消任务',
    retry: '重试',
    proceed: '继续执行',
    startNewTask: '开始新的任务',
    runCommand: '运行命令',
    resumeTask: '恢复任务',
    proceedWhileRunning: '保持运行并继续',
  },
  welcome: {
    title: `代码洪流，一念即成`,
    examples: ['生成一个贪吃蛇游戏', '生成一个番茄时钟'] as const,
  },
  header: {
    newChat: '新的聊天',
    history: '历史会话',
    tools: '工具市场',
  },
  chat: {
    thinking: `${JOYCODE_BRAND_NAME} 思考中`,
    placeholder: {
      chat: placeholder,
      architect: placeholder,
      code: placeholder,
      orchestrator: placeholder,
      debug: placeholder,
      ask: placeholder,
      test: placeholder,

      default: placeholder,
    },
  },
  buttons: {
    primary: {
      editFile: '保存',
      default: '同意',
      approve: '同意',
      command: '运行命令',
      retry: '重试',
      proceed: '继续执行',
      startNew: '开始新的任务',
      resume: '恢复任务',
    },
    secondary: {
      editFile: '拒绝',
      default: '拒绝',
      reject: '拒绝',
      startNew: '开始新的任务',
    },
  },
};
