import { AutoApprovalSettings } from '../../../src/shared/AutoApprovalSettings';

export const ACTION_METADATA: {
  id: keyof AutoApprovalSettings['actions'];
  label: string;
  shortName: string;
  description: string;
}[] = [
  {
    id: 'readFiles',
    label: '读取文件和目录',
    // label: 'Read files and directories',
    shortName: '读取权限',
    description: '允许自动读取工作空间的文件。',
  },
  {
    id: 'editFiles',
    label: '编辑文件、切换模式、执行子任务',
    shortName: '写入权限',
    description: '允许自动修改工作空间的文件、切换模式、执行子任务，请慎重勾选。',
  },
  {
    id: 'executeCommands',
    label: '执行安全的终端指令',
    shortName: '命令行操作',
    description: '允许自动执行安全的终端指令。当模型检测到命令存在潜在风险时，依然需要您手动批准执行。',
  },
  {
    id: 'useBrowser',
    label: '进行浏览器交互',
    shortName: '浏览器交互',
    description: '允许自动操作浏览器与 Web 页面交互。',
  },
  {
    id: 'todoList',
    label: '创建与控制待办事项列表',
    shortName: '待办事项操作',
    description: '将自动批准模型对待办事项的创建、修改等操作。',
  },
];
