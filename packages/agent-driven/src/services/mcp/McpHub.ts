import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import {
  CallToolResultSchema,
  ListResourcesResultSchema,
  ListResourceTemplatesResultSchema,
  ListToolsResultSchema,
  ReadResourceResultSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import ReconnectingEventSource from 'reconnecting-eventsource';
import chokidar, { FSWatcher } from 'chokidar';
import delay from 'delay';
import deepEqual from 'fast-deep-equal';
import * as fs from 'fs/promises';
import * as files from 'fs-extra';
import * as os from 'os';
import * as path from 'path';
import * as vscode from 'vscode';
import { z } from 'zod';
import { decode } from 'iconv-lite';
import { exec } from 'child_process';
import { promisify } from 'util';
import { JoyCoderProvider } from '../../core/webview/JoycoderProvider';
import { ExtensionMessage } from '../../shared/ExtensionMessage';
import {
  DEFAULT_MCP_TIMEOUT_SECONDS,
  McpMode,
  McpResource,
  McpResourceResponse,
  McpResourceTemplate,
  McpServer,
  McpTool,
  McpToolCallResponse,
  MIN_MCP_TIMEOUT_SECONDS,
  MCP_SETTINGS_SCHEMA,
} from '@joycoder/shared/src/mcp/mcp';

import { fileExistsAtPath } from '../../utils/fs';
import { arePathsEqual } from '../../utils/path';
import { JoyCoderMCPMessageMap } from '../../adaptor/translate/message';
import { secondsToMs } from '../../utils/time';
import { GlobalFileNames } from '../../core/storage/disk';
import osName from 'os-name';
import { FileSystemHelper } from '../../utils/FileSystemHelper';
import { isRemoteEnvironment } from '../../utils/fs';
import { McpSettingsError, NoWorkspaceFolderError, readAndParseFile } from './McpSettingsUtils';
import { notifyWebviewOfServerChanges } from './mcpDrivenResExecute';
// Default timeout for internal MCP data requests in milliseconds; is not the same as the user facing timeout stored as DEFAULT_MCP_TIMEOUT_SECONDS
const DEFAULT_REQUEST_TIMEOUT_MS = 5000;

export type McpConnection = {
  server: McpServer;
  client: Client;
  transport: StdioClientTransport | SSEClientTransport | StreamableHTTPClientTransport;
};

export type McpTransportType = 'stdio' | 'sse' | 'streamableHttp';

export type McpServerConfig = z.infer<typeof ServerConfigSchema>;

export type ProjectType = 'global' | 'project' | 'initial';

const AutoApproveSchema = z.array(z.string()).default([]);

const BaseConfigSchema = z.object({
  isProject: z.boolean().optional(),
  autoApprove: AutoApproveSchema.optional(),
  disabled: z.boolean().optional(),
  timeout: z.number().min(MIN_MCP_TIMEOUT_SECONDS).optional().default(DEFAULT_MCP_TIMEOUT_SECONDS),
});

const SseConfigSchema = BaseConfigSchema.extend({
  url: z.string().url(),
  type: z.literal('sse').optional(),
  transportType: z.string().optional(), // Support legacy field
  headers: z.record(z.string()).optional(),
  args: z.array(z.string()).optional(),
  env: z.record(z.string()).optional(),
  command: z.string().optional(),
}).transform((config) => {
  // Support both type and transportType fields
  const finalType = config.type || (config.transportType === 'sse' ? 'sse' : undefined) || 'sse';
  return {
    ...config,
    type: finalType as 'sse',
    transportType: 'sse' as const,
  };
});

const StdioConfigSchema = BaseConfigSchema.extend({
  command: z.string(),
  type: z.literal('stdio').optional(),
  transportType: z.string().optional(), // Support legacy field
  args: z.array(z.string()).optional(),
  cwd: z.string().optional(),
  env: z.record(z.string()).optional(),
  autoApprove: AutoApproveSchema.optional(),
  disabled: z.boolean().optional(),
  timeout: z.number().min(MIN_MCP_TIMEOUT_SECONDS).optional().default(DEFAULT_MCP_TIMEOUT_SECONDS),
  url: z.string().optional(),
  headers: z.record(z.string()).optional(),
}).transform((data) => {
  // Support both type and transportType fields
  const finalType = data.type || (data.transportType === 'stdio' ? 'stdio' : undefined) || 'stdio';
  return {
    ...data,
    type: finalType as 'stdio',
    // Remove the legacy field after transformation
    transportType: undefined,
  };
});

const StreamableHttpSchema = BaseConfigSchema.extend({
  type: z.literal('streamableHttp').optional(),
  transportType: z.string().optional(), // Support legacy field
  url: z.string().url('URL must be a valid URL format'),
  headers: z.record(z.string()).optional(),
  // Allow other fields for backward compatibility
  command: z.string().optional(),
  args: z.array(z.string()).optional(),
  env: z.record(z.string()).optional(),
}).transform((data) => {
  // Support both type and transportType fields
  // Note: legacy transportType was "http" not "streamableHttp"
  const finalType = data.type || (data.transportType === 'http' ? 'streamableHttp' : undefined) || 'streamableHttp';
  return {
    ...data,
    type: finalType as 'streamableHttp',
    // Remove the legacy field after transformation
    transportType: undefined,
  };
});

const ServerConfigSchema = z.union([StdioConfigSchema, SseConfigSchema, StreamableHttpSchema]);

const McpSettingsSchema = z.object({
  mcpServers: z.record(ServerConfigSchema),
});

export class McpHub {
  private providerRef: WeakRef<JoyCoderProvider>;
  private disposables: vscode.Disposable[] = [];
  private settingsWatcher?: vscode.FileSystemWatcher;
  private fileWatchers: Map<string, FSWatcher> = new Map();
  connections: McpConnection[] = [];
  isConnecting: boolean = false;
  static encoding: string | undefined;

  constructor(provider: JoyCoderProvider) {
    this.providerRef = new WeakRef(provider);
    this.watchMcpSettingsFile();
    this.initializeMcpServers();
  }

  static async getCodePage() {
    const execPromise = promisify(exec);
    try {
      const { stdout, stderr } = await execPromise('chcp');
      const codePage = stdout.match(/\d+/)?.[0];
      return codePage;
    } catch (error) {
      console.error(`执行错误: ${error}`);
      throw error;
    }
  }
  static async getStdioEncoding() {
    if (this.encoding) return this.encoding;
    if (osName().toLowerCase().includes('windows')) {
      const codePage = await this.getCodePage();
      switch (codePage) {
        case '936':
          this.encoding = 'cp936';
          break;
        case '950':
          this.encoding = 'cp950';
          break;
        default:
          this.encoding = 'utf8';
          break;
      }
    } else {
      this.encoding = 'utf8';
    }
    return this.encoding;
  }

  getServers(): McpServer[] {
    // Only return enabled servers
    return this.connections.filter((conn) => !conn.server.disabled).map((conn) => conn.server);
  }

  getMode(): McpMode {
    return vscode.workspace.getConfiguration('JoyCode.mcp').get<McpMode>('mode', 'full');
  }

  async getMcpServersPath(): Promise<string> {
    const provider = this.providerRef.deref();
    if (!provider) {
      throw new Error(JoyCoderMCPMessageMap['Provider not available']);
    }
    const mcpServersPath = await provider.ensureMcpServersDirectoryExists();
    return mcpServersPath;
  }

  async getMcpSettingsFilePath(projectType: ProjectType = 'global'): Promise<string> {
    const isRemoteEnv = isRemoteEnvironment();
    const isProjectEnv = ['project', 'initial'].includes(projectType);
    const provider = this.providerRef.deref();
    if (!provider) {
      throw new Error(JoyCoderMCPMessageMap['Provider not available']);
    }

    let localPath = '';
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (isProjectEnv) {
      if (workspaceFolders && workspaceFolders.length > 0) {
        // 获取第一个工作区文件夹的路径
        localPath = workspaceFolders[0].uri.fsPath;
      } else {
        // 未打开工作区
        vscode.window.showInformationMessage(JoyCoderMCPMessageMap['No workspace folder open']);
        return '';
      }
    }

    const rootPath = isProjectEnv ? localPath : os.homedir();
    let mcpSettingsFilePath: string | vscode.Uri = '',
      fileExists: boolean = false;
    const mcpConfigName = isProjectEnv ? GlobalFileNames.projectMcpSettings : GlobalFileNames.mcpSettings;
    if (isRemoteEnv) {
      try {
        if (isProjectEnv) {
          mcpSettingsFilePath = path.posix.join('.joycode', mcpConfigName);
          await FileSystemHelper.access(mcpSettingsFilePath);
        } else {
          mcpSettingsFilePath = path.posix.join(rootPath, '.joycode', mcpConfigName);
          await fs.access(mcpSettingsFilePath);
        }
        fileExists = true;
      } catch (error) {
        fileExists = false;
      }
    } else {
      mcpSettingsFilePath = path.posix.join(rootPath, '.joycode', mcpConfigName);
      fileExists = await fileExistsAtPath(mcpSettingsFilePath);
    }

    //const mcpSettingsFileUri = FileSystemHelper.getUri(mcpSettingsFilePath);
    // console.log('mcpSettingsFileUri',mcpSettingsFileUri);
    if (!fileExists) {
      if (projectType === 'global' || projectType === 'initial') {
        const folderPath = isRemoteEnv ? path.posix.dirname(mcpSettingsFilePath) : path.dirname(mcpSettingsFilePath);
        const uri = isRemoteEnv ? folderPath : FileSystemHelper.getUri(folderPath);
        // 创建文件目录
        await FileSystemHelper.mkdir(uri, { recursive: true });

        // await files.ensureDir(dirPath);
        if (projectType === 'global') {
          await files.writeFile(mcpSettingsFilePath, MCP_SETTINGS_SCHEMA);
        } else {
          await FileSystemHelper.writeFile(mcpSettingsFilePath, MCP_SETTINGS_SCHEMA);
        }
      } else {
        // 项目配置文件不存在，只在initial模式下创建
        return '';
      }
    }
    // return isRemoteEnv ? mcpSettingsFileUri.fsPath : mcpSettingsFilePath;
    return mcpSettingsFilePath;
  }

  public async saveOrUpdateServer(
    serverParam: string,
    projectType: ProjectType = 'global'
  ): Promise<z.infer<typeof McpSettingsSchema> | undefined> {
    try {
      const serverParamObj = JSON.parse(serverParam);
      const mcpConfigResult = McpSettingsSchema.safeParse(serverParamObj);
      if (!mcpConfigResult.success) {
        throw new McpSettingsError('MCP schema验证失败, 请检查配置文件是否符合MCP schema规范:' + serverParam);
      }

      const [globalSettingsPath, projectSettingsPath] = await Promise.all([
        this.getMcpSettingsFilePath(),
        this.getMcpSettingsFilePath('initial'),
      ]);

      const isProjectType = ['project', 'initial'].includes(projectType);

      if (isProjectType && projectSettingsPath === '') {
        throw new NoWorkspaceFolderError(JoyCoderMCPMessageMap['No workspace folder open']);
      }

      let settingsPath = isProjectType ? projectSettingsPath : globalSettingsPath;

      if (isProjectType) {
        await FileSystemHelper.access(settingsPath);
      } else {
        await files.access(settingsPath);
      }

      const globalConfigRaw = await fs.readFile(globalSettingsPath, 'utf-8');
      const projectConfig = projectSettingsPath ? await readAndParseFile(projectSettingsPath) : { mcpServers: {} };

      const globalConfig = typeof globalConfigRaw === 'string' ? JSON.parse(globalConfigRaw) : globalConfigRaw;

      const serverParamConfig = serverParamObj.mcpServers;
      const serverKey = Object.keys(serverParamConfig)[0];

      const targetConfig = isProjectType ? projectConfig : globalConfig;

      if (!targetConfig.mcpServers) {
        targetConfig.mcpServers = {};
      }

      targetConfig.mcpServers[serverKey] = {
        ...(targetConfig.mcpServers[serverKey] || {}),
        ...serverParamConfig[serverKey],
      };

      if (isProjectType) {
        await FileSystemHelper.writeFile(settingsPath, JSON.stringify(targetConfig, null, 2));
      } else {
        await files.writeFile(settingsPath, JSON.stringify(targetConfig, null, 2));
      }

      const mergedConfig = {
        ...globalConfig,
        mcpServers: {
          ...globalConfig.mcpServers,
          ...projectConfig.mcpServers,
        },
      };
      const parsedConfig = McpSettingsSchema.safeParse(mergedConfig);
      if (!parsedConfig.success) {
        throw new McpSettingsError('配置文件更新成功, 但schema验证失败, 请检查配置文件是否符合schema规范');
        // throw new McpSettingsError(
        //   'Configuration update successful, but schema validation failed. Invalid MCP settings schema.'
        // );
      }
      //vscode.window.showInformationMessage(`MCP service saved or updated successfully`);
      vscode.window.showInformationMessage(`MCP 服务添加/更新成功`);
      return parsedConfig.data;
    } catch (error) {
      if (error instanceof NoWorkspaceFolderError) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : String(error);
      vscode.window.showErrorMessage(`MCP 服务添加/更新失败 : ${errorMessage}`);
      throw new Error(`MCP 服务添加/更新失败: ${errorMessage}`);
    }
  }
  public async readAndValidateMcpSettingsFile(): Promise<z.infer<typeof McpSettingsSchema> | undefined> {
    try {
      const glbaolSettingFilePath = await this.getMcpSettingsFilePath();
      // 远程模式不支持全局变量，因此全局都是读取本地文件
      const glbaolContent = await files.readFile(glbaolSettingFilePath, 'utf-8');

      if (glbaolContent === '') return undefined;
      let glbaolConfig: z.infer<typeof McpSettingsSchema> = { mcpServers: {} };

      try {
        glbaolConfig = JSON.parse(glbaolContent);
      } catch (error) {
        vscode.window.showErrorMessage(
          JoyCoderMCPMessageMap[
            'Invalid MCP settings format. Please ensure your settings follow the correct JSON format.'
          ]
        );
        return undefined;
      }

      // Validate against schema
      const glbaolConfigResult = McpSettingsSchema.safeParse(glbaolConfig);
      if (!glbaolConfigResult.success) {
        vscode.window.showErrorMessage(JoyCoderMCPMessageMap['Invalid global MCP settings schema.']);
        return undefined;
      }

      const mcpSettingsPath = await this.getMcpSettingsFilePath('project');
      // 如果存在项目配置文件，则合并全局配置和项目配置
      if (mcpSettingsPath !== '') {
        const mcpContent = await FileSystemHelper.readFile(mcpSettingsPath, 'utf-8');

        if (mcpContent === '') return undefined;
        let mcpConfig: z.infer<typeof McpSettingsSchema> = { mcpServers: {} };

        // Parse JSON file content
        try {
          mcpConfig = JSON.parse(mcpContent);
        } catch (error) {
          vscode.window.showErrorMessage(
            JoyCoderMCPMessageMap[
              'Invalid MCP settings format. Please ensure your settings follow the correct JSON format.'
            ]
          );

          return undefined;
        }

        // Validate against schema
        const mcpConfigResult = McpSettingsSchema.safeParse(mcpConfig);
        if (!mcpConfigResult.success) {
          vscode.window.showErrorMessage(JoyCoderMCPMessageMap['Invalid project MCP settings schema.']);
          return undefined;
        }

        // 将mcpConfigResult.data.mcpServers中的每个server添加isProject:true标记
        Object.keys(mcpConfigResult.data.mcpServers).forEach((serverName) => {
          mcpConfigResult.data.mcpServers[serverName] = {
            ...mcpConfigResult.data.mcpServers[serverName],
            isProject: true,
          };
        });

        glbaolConfigResult.data.mcpServers = {
          ...glbaolConfigResult.data.mcpServers,
          ...mcpConfigResult.data.mcpServers,
        };
      }

      return glbaolConfigResult.data;
    } catch (error) {
      console.error('Failed to read MCP settings:', error);
      return undefined;
    }
  }

  private async handleSettingsUpdate(): Promise<void> {
    const settings = await this.readAndValidateMcpSettingsFile();
    if (settings) {
      try {
        vscode.window.showInformationMessage(JoyCoderMCPMessageMap['Updating MCP servers...']);
        await this.updateServerConnections(settings.mcpServers);

        vscode.window.showInformationMessage(JoyCoderMCPMessageMap['MCP servers updated']);
      } catch (error) {
        console.error('Failed to process MCP settings change:', error);
      }
    }
  }

  async updateMcpServers(): Promise<void> {
    await this.handleSettingsUpdate();
  }

  private async watchMcpSettingsFile(): Promise<void> {
    const settingsPath = await this.getMcpSettingsFilePath();

    let mcpSettingsPath = '';
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (workspaceFolders && workspaceFolders.length > 0) {
      mcpSettingsPath = path.join(workspaceFolders[0].uri.fsPath, '.joycode', GlobalFileNames.projectMcpSettings);
    }

    this.disposables.push(
      vscode.workspace.onDidSaveTextDocument(async (document) => {
        if (arePathsEqual(document.uri.fsPath, settingsPath) || arePathsEqual(document.uri.fsPath, mcpSettingsPath)) {
          await this.handleSettingsUpdate();
        }
      })
    );
  }

  private async initializeMcpServers(): Promise<void> {
    const settings = await this.readAndValidateMcpSettingsFile();
    if (settings) {
      await this.updateServerConnections(settings.mcpServers);
    }
  }

  private async connectToServer(
    name: string,
    config: z.infer<typeof StdioConfigSchema> | z.infer<typeof SseConfigSchema> | z.infer<typeof StreamableHttpSchema>
  ): Promise<void> {
    // Remove existing connection if it exists (should never happen, the connection should be deleted beforehand)
    this.connections = this.connections.filter((conn) => conn.server.name !== name);

    try {
      // Each MCP server requires its own transport connection and has unique capabilities, configurations, and error handling. Having separate clients also allows proper scoping of resources/tools and independent server management like reconnection.
      const client = new Client(
        {
          name: 'JoyCode',
          version: this.providerRef.deref()?.context.extension?.packageJSON?.version ?? '1.0.0',
        },
        {
          capabilities: {},
        }
      );
      let transport: StdioClientTransport | SSEClientTransport | StreamableHTTPClientTransport;
      if (config.type === 'sse') {
        // transport = new SSEClientTransport(new URL(config.url), {});
        const sseOptions = {
          requestInit: {
            headers: config.headers,
          },
        };
        const reconnectingEventSourceOptions = {
          max_retry_time: 5000,
          withCredentials: config.headers?.['Authorization'] ? true : false,
        };
        global.EventSource = ReconnectingEventSource;
        transport = new SSEClientTransport(new URL(config.url), {
          ...sseOptions,
          eventSourceInit: reconnectingEventSourceOptions,
        });

        transport.onerror = async (error) => {
          console.error(`Transport error for "${name}":`, error);
          const connection = this.findConnection(name);
          if (connection) {
            connection.server.status = 'disconnected';
            this.appendErrorMessage(connection, error instanceof Error ? error.message : `${error}`);
          }
          await this.notifyWebviewOfServerChanges();
        };
      } else if (config.type === 'streamableHttp') {
        transport = new StreamableHTTPClientTransport(new URL(config.url), {
          requestInit: {
            headers: config.headers,
          },
        });
        transport.onerror = async (error) => {
          console.error(`Transport error for "${name}":`, error);
          const connection = this.findConnection(name);
          if (connection) {
            connection.server.status = 'disconnected';
            this.appendErrorMessage(connection, error instanceof Error ? error.message : `${error}`);
          }
          await this.notifyWebviewOfServerChanges();
        };
      } else {
        const envString = process.env.PATH ? process.env.PATH : global?.process?.env?.PATH || '';
        transport = new StdioClientTransport({
          command: config.command,
          args: config.args,
          env: {
            ...config.env,
            ...(envString ? { PATH: envString } : {}),
            // ...(process.env.NODE_PATH ? { NODE_PATH: process.env.NODE_PATH } : {}),
          },
          stderr: 'pipe', // necessary for stderr to be available
        });
      }

      transport.onerror = async (error) => {
        console.error(`"${name}"的传输错误:`, error);
        const connection = this.connections.find((conn) => conn.server.name === name);
        if (connection) {
          connection.server.status = 'disconnected';
          this.appendErrorMessage(connection, error.message);
        }
        await this.notifyWebviewOfServerChanges();
      };

      transport.onclose = async () => {
        const connection = this.connections.find((conn) => conn.server.name === name);
        if (connection) {
          connection.server.status = 'disconnected';
        }
        await this.notifyWebviewOfServerChanges();
      };

      const connection: McpConnection = {
        server: {
          name,
          config: JSON.stringify(config),
          status: 'connecting',
          disabled: config.disabled,
        },
        client,
        transport,
      };
      this.connections.push(connection);

      if (config.type === 'stdio') {
        // transport.stderr is only available after the process has been started. However we can't start it separately from the .connect() call because it also starts the transport. And we can't place this after the connect call since we need to capture the stderr stream before the connection is established, in order to capture errors during the connection process.
        // As a workaround, we start the transport ourselves, and then monkey-patch the start method to no-op so that .connect() doesn't try to start it again.
        await transport.start();
        const stderrStream = (transport as StdioClientTransport).stderr;
        if (stderrStream) {
          stderrStream.on('data', async (data: Buffer) => {
            // 使用对应系统编码进行解码，防止乱码
            const output = decode(data, await McpHub.getStdioEncoding());

            // Check if output contains INFO level log
            const isInfoLog = /^\s*INFO\b/.test(output);

            if (isInfoLog) {
              // Log normal informational messages
              console.info(`服务 "${name}" info:`, output);
            } else {
              // Treat as error log
              console.error(`服务 "${name}" stderr:`, output);
              const connection = this.connections.find((conn) => conn.server.name === name);
              if (connection) {
                this.appendErrorMessage(connection, output);
                // Only notify webview if server is already disconnected
                if (connection.server.status === 'disconnected') {
                  await this.notifyWebviewOfServerChanges();
                }
              }
            }
          });
        } else {
          console.error(`${name}没有stderr流`);
        }
        transport.start = async () => {}; // No-op now, .connect() won't fail
      }

      // Connect
      await client.connect(transport);
      connection.server.status = 'connected';
      connection.server.error = '';

      // Initial fetch of tools and resources
      connection.server.tools = await this.fetchToolsList(name);
      connection.server.resources = await this.fetchResourcesList(name);
      connection.server.resourceTemplates = await this.fetchResourceTemplatesList(name);
    } catch (error) {
      // Update status with error
      const connection = this.connections.find((conn) => conn.server.name === name);
      if (connection) {
        connection.server.status = 'disconnected';
        this.appendErrorMessage(connection, error instanceof Error ? error.message : String(error));
      }
      throw error;
    }
  }

  private findConnection(name: string, source?: 'rpc' | 'internal'): McpConnection | undefined {
    return this.connections.find((conn) => conn.server.name === name);
  }

  private appendErrorMessage(connection: McpConnection, error: string) {
    // 过滤掉包含INFO级别的错误信息
    if (error.includes('Processing request of type')) {
      console.warn('mcp info (filtered)', error);
      return;
    }
    if (error.includes('DEBUG')) {
      console.warn('mcp DEBUG (filtered)', error);
      return;
    }
    const newError = connection.server.error ? `${connection.server.error}\n${error}` : error;
    connection.server.error = newError; //.slice(0, 800)
  }

  private async fetchToolsList(serverName: string): Promise<McpTool[]> {
    try {
      const connection = this.connections.find((conn) => conn.server.name === serverName);

      if (!connection) {
        throw new Error(`No connection found for server: ${serverName}`);
      }
      // Disabled servers don't have clients, so return empty tools list
      if (connection.server.disabled || !connection.client) {
        return [];
      }

      const response = await connection.client.request({ method: 'tools/list' }, ListToolsResultSchema, {
        timeout: DEFAULT_REQUEST_TIMEOUT_MS,
      });

      // Get autoApprove settings
      const config = await this.readAndValidateMcpSettingsFile();
      const autoApproveConfig = config?.mcpServers[serverName]?.autoApprove || [];

      // Mark tools as always allowed based on settings
      const tools = (response?.tools || []).map((tool) => ({
        ...tool,
        autoApprove: autoApproveConfig.includes(tool.name),
      }));

      return tools;
    } catch (error) {
      return [];
    }
  }

  private async fetchResourcesList(serverName: string): Promise<McpResource[]> {
    try {
      const connection = this.connections.find((conn) => conn.server.name === serverName);

      // Disabled servers don't have clients, so return empty resource templates list
      if (!connection || connection.server.disabled || !connection.client) {
        return [];
      }
      const response = await this.connections
        .find((conn) => conn.server.name === serverName)
        ?.client.request({ method: 'resources/list' }, ListResourcesResultSchema);
      return response?.resources || [];
    } catch (error) {
      // console.error(`Failed to fetch resources for ${serverName}:`, error)
      return [];
    }
  }

  private async fetchResourceTemplatesList(serverName: string): Promise<McpResourceTemplate[]> {
    try {
      const connection = this.connections.find((conn) => conn.server.name === serverName);

      // Disabled servers don't have clients, so return empty resource templates list
      if (!connection || connection.server.disabled || !connection.client) {
        return [];
      }

      const response = await this.connections
        .find((conn) => conn.server.name === serverName)
        ?.client.request({ method: 'resources/templates/list' }, ListResourceTemplatesResultSchema, {
          timeout: DEFAULT_REQUEST_TIMEOUT_MS,
        });
      return response?.resourceTemplates || [];
    } catch (error) {
      // console.error(`Failed to fetch resource templates for ${serverName}:`, error)
      return [];
    }
  }

  async deleteConnection(name: string): Promise<void> {
    const connection = this.connections.find((conn) => conn.server.name === name);
    if (connection) {
      try {
        await connection.transport.close();
        await connection.client.close();
      } catch (error) {
        console.error(`Failed to close transport for ${name}:`, error);
      }
      this.connections = this.connections.filter((conn) => conn.server.name !== name);
    }
  }

  async updateServerConnections(newServers: Record<string, any>): Promise<void> {
    this.isConnecting = true;
    this.removeAllFileWatchers();
    const currentNames = new Set(this.connections.map((conn) => conn.server.name));
    const newNames = new Set(Object.keys(newServers));

    // Delete removed servers
    for (const name of currentNames) {
      if (!newNames.has(name)) {
        await this.deleteConnection(name);
        console.log(`Deleted MCP server: ${name}`);
      }
    }

    // Update or add servers
    for (const [name, config] of Object.entries(newServers)) {
      const currentConnection = this.connections.find((conn) => conn.server.name === name);

      if (!currentConnection) {
        // New server
        try {
          if (config.transportType === 'stdio') {
            this.setupFileWatcher(name, config);
          }

          await this.connectToServer(name, config);
        } catch (error) {
          console.error(`Failed to connect to new MCP server ${name}:`, error);
        }
      } else if (!deepEqual(JSON.parse(currentConnection.server.config), config)) {
        // Existing server with changed config
        try {
          if (config.transportType === 'stdio') {
            this.setupFileWatcher(name, config);
          }

          await this.deleteConnection(name);
          await this.connectToServer(name, config);
          console.log(`Reconnected MCP server with updated config: ${name}`);
        } catch (error) {
          console.error(`Failed to reconnect MCP server ${name}:`, error);
        }
      }
      // If server exists with same config, do nothing
    }
    await this.notifyWebviewOfServerChanges();
    this.isConnecting = false;
  }

  private setupFileWatcher(name: string, config: any) {
    const filePath = config.args?.find((arg: string) => arg.includes('build/index.js'));
    if (filePath) {
      // we use chokidar instead of onDidSaveTextDocument because it doesn't require the file to be open in the editor. The settings config is better suited for onDidSave since that will be manually updated by the user or JoyCoder (and we want to detect save events, not every file change)
      const watcher = chokidar.watch(filePath, {
        // persistent: true,
        // ignoreInitial: true,
        // awaitWriteFinish: true, // This helps with atomic writes
      });

      watcher.on('change', () => {
        console.log(`Detected change in ${filePath}. Restarting server ${name}...`);
        this.restartConnection(name);
      });

      this.fileWatchers.set(name, watcher);
    }
  }

  private removeAllFileWatchers() {
    this.fileWatchers.forEach((watcher) => watcher.close());
    this.fileWatchers.clear();
  }

  async restartConnection(serverName: string): Promise<void> {
    this.isConnecting = true;
    const provider = this.providerRef.deref();
    if (!provider) {
      return;
    }

    // Get existing connection and update its status
    const connection = this.connections.find((conn) => conn.server.name === serverName);
    const config = connection?.server.config;
    if (config) {
      vscode.window.showInformationMessage(`正在重启 ${serverName} MCP 服务器...`);
      connection.server.status = 'connecting';
      connection.server.error = '';
      await this.notifyWebviewOfServerChanges();
      await delay(500); // artificial delay to show user that server is restarting
      try {
        await this.deleteConnection(serverName);
        // Try to connect again using existing config
        await this.connectToServer(serverName, JSON.parse(config));
        vscode.window.showInformationMessage(`${serverName} MCP 服务器连接成功`);
      } catch (error) {
        console.error(`Failed to restart connection for ${serverName}:`, error);
        vscode.window.showErrorMessage(`无法连接到 ${serverName} MCP 服务器`);
      }
    }

    await this.notifyWebviewOfServerChanges();
    this.isConnecting = false;
  }

  public async notifyWebviewOfServerChanges(): Promise<McpServer[]> {
    // servers should always be sorted in the order they are defined in the settings file
    const config = await this.readAndValidateMcpSettingsFile();
    const serverOrder = Object.keys(config?.mcpServers || {});

    // 1. 准备要发送的数据
    const sortedServers = [...this.connections]
      .sort((a, b) => {
        const indexA = serverOrder.indexOf(a.server.name);
        const indexB = serverOrder.indexOf(b.server.name);
        return indexA - indexB;
      })
      .map((connection) => connection.server);

    // 2. 创建消息对象
    const message: ExtensionMessage = {
      type: 'mcpServers',
      mcpServers: sortedServers,
    };
    await this.providerRef.deref()?.postMessageToWebview(message);
    await notifyWebviewOfServerChanges(message);
    return sortedServers;
  }

  public async getMcpServers(): Promise<McpServer[]> {
    const config = await this.readAndValidateMcpSettingsFile();
    const serverOrder = Object.keys(config?.mcpServers || {});
    return this.connections
      .sort((a, b) => {
        const indexA = serverOrder.indexOf(a.server.name);
        const indexB = serverOrder.indexOf(b.server.name);
        return indexA - indexB;
      })
      .map((connection) => {
        const serverConfig = config?.mcpServers[connection.server.name];
        return {
          ...connection.server,
          isProject: serverConfig?.isProject || false,
        };
      });
  }

  async sendLatestMcpServers() {
    await this.notifyWebviewOfServerChanges();
  }

  // Using server

  // Public methods for server management

  public async toggleServerDisabled(serverName: string, disabled: boolean, projectType?: ProjectType): Promise<void> {
    try {
      const isProject = projectType === 'project';
      //let isGlobalConfigFile = true; // 默认是全局配置文件
      let settingsPath = isProject ? await this.getMcpSettingsFilePath('initial') : await this.getMcpSettingsFilePath();

      // Ensure the settings file exists and is accessible
      try {
        if (isProject) {
          await FileSystemHelper.access(settingsPath);
        } else {
          await files.access(settingsPath);
        }
      } catch (error) {
        console.error('Settings file not accessible:', error);
        throw new Error('设置文件无法访问');
      }
      let content;
      if (isProject) {
        content = await FileSystemHelper.readFile(settingsPath, 'utf-8');
      } else {
        content = await files.readFile(settingsPath, 'utf-8');
      }
      let config = JSON.parse(content);

      // Validate the config structure
      if (!config || typeof config !== 'object') {
        throw new Error('无效的配置结构');
      }

      if (!config.mcpServers || typeof config.mcpServers !== 'object') {
        config.mcpServers = {};
      }

      // Create a new server config object to ensure clean structure
      const serverConfig = {
        ...config.mcpServers[serverName],
        disabled,
      };

      // Ensure required fields exist
      if (!serverConfig.autoApprove) {
        serverConfig.autoApprove = [];
      }

      config.mcpServers[serverName] = serverConfig;

      // Write the entire config back
      const updatedConfig = {
        mcpServers: config.mcpServers,
      };

      if (!isProject) {
        await files.writeFile(settingsPath, JSON.stringify(updatedConfig, null, 2));
      } else {
        await FileSystemHelper.writeFile(settingsPath, JSON.stringify(updatedConfig, null, 2));
      }

      const connection = this.connections.find((conn) => conn.server.name === serverName);
      if (connection) {
        try {
          connection.server.disabled = disabled;
          connection.server.config = JSON.stringify(serverConfig);
          // Only refresh capabilities if connected
          if (connection.server.status === 'connected') {
            connection.server.tools = await this.fetchToolsList(serverName);
            connection.server.resources = await this.fetchResourcesList(serverName);
            connection.server.resourceTemplates = await this.fetchResourceTemplatesList(serverName);
          }
        } catch (error) {
          console.error(`Failed to refresh capabilities for ${serverName}:`, error);
        }
      }

      await this.notifyWebviewOfServerChanges();
    } catch (error) {
      console.error('Failed to update server disabled state:', error);
      if (error instanceof Error) {
        console.error('Error details:', error.message, error.stack);
      }
      vscode.window.showErrorMessage(`无法更新服务器状态：${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  async readResource(serverName: string, uri: string): Promise<McpResourceResponse> {
    const connection = this.connections.find((conn) => conn.server.name === serverName);
    if (!connection) {
      throw new Error(`未找到服务器的连接： ${serverName}`);
    }
    if (connection.server.disabled) {
      throw new Error(`服务器 “${serverName}” 已被禁用，请启用后再试。`);
    }
    return await connection.client.request(
      {
        method: 'resources/read',
        params: {
          uri,
        },
      },
      ReadResourceResultSchema
    );
  }

  async callTool(
    serverName: string,
    toolName: string,
    toolArguments?: Record<string, any>
  ): Promise<McpToolCallResponse> {
    const connection = this.connections.find((conn) => conn.server.name === serverName);
    if (!connection) {
      throw new Error(`未找到服务器 ${serverName} 的连接。请确保使用“已连接的 MCP 服务器”下可用的 MCP 服务器。`);
    }

    if (connection.server.disabled) {
      throw new Error(`服务器 "${serverName}" 已被禁用，无法使用，请启用后再试。`);
    }
    let timeout = secondsToMs(DEFAULT_MCP_TIMEOUT_SECONDS); // sdk expects ms

    try {
      const config = JSON.parse(connection.server.config);
      const parsedConfig = StdioConfigSchema.parse(config);
      timeout = secondsToMs(parsedConfig.timeout);
    } catch (error) {
      console.error(`无法解析服务器的超时配置 ${serverName}: ${error}`);
    }
    //@ts-ignore
    return await connection.client.request(
      {
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: toolArguments,
        },
      },
      CallToolResultSchema,
      {
        timeout,
      }
    );
  }
  public async updateServerTimeout(serverName: string, timeout: number): Promise<void> {
    try {
      // Validate timeout against schema
      const setConfigResult = BaseConfigSchema.shape.timeout.safeParse(timeout);
      if (!setConfigResult.success) {
        throw new Error(`无效的超时时间：${timeout}。必须至少为 ${MIN_MCP_TIMEOUT_SECONDS} 秒。`);
      }

      const settingsPath = await this.getMcpSettingsFilePath();
      const content = await fs.readFile(settingsPath, 'utf-8');
      const config = JSON.parse(content);

      if (!config.mcpServers?.[serverName]) {
        throw new Error(`服务器“${serverName}”未在设置中找到`);
      }

      config.mcpServers[serverName] = {
        ...config.mcpServers[serverName],
        timeout,
      };

      await fs.writeFile(settingsPath, JSON.stringify(config, null, 2));

      await this.updateServerConnections(config.mcpServers);
    } catch (error) {
      console.error('Failed to update server timeout:', error);
      if (error instanceof Error) {
        console.error('Error details:', error.message, error.stack);
      }
      vscode.window.showErrorMessage(`更新服务器超时： ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  async toggleToolAutoApprove(
    serverName: string,
    toolName: string,
    shouldAllow: boolean,
    projectType?: ProjectType
  ): Promise<void> {
    try {
      const isProject = projectType === 'project';

      // let isGlobalConfigFile = true; // 操作本地全局文件
      let settingsPath = isProject ? await this.getMcpSettingsFilePath('initial') : await this.getMcpSettingsFilePath();

      let content;
      if (isProject) {
        content = await FileSystemHelper.readFile(settingsPath, 'utf-8');
      } else {
        content = await files.readFile(settingsPath, 'utf-8');
      }

      let config = JSON.parse(content);

      // Initialize autoApprove if it doesn't exist
      if (!config.mcpServers[serverName].autoApprove) {
        config.mcpServers[serverName].autoApprove = [];
      }

      const autoApprove = config.mcpServers[serverName].autoApprove;
      const toolIndex = autoApprove.indexOf(toolName);

      if (shouldAllow && toolIndex === -1) {
        // Add tool to autoApprove list
        autoApprove.push(toolName);
      } else if (!shouldAllow && toolIndex !== -1) {
        // Remove tool from autoApprove list
        autoApprove.splice(toolIndex, 1);
      }

      // Write updated config back to file
      if (!isProject) {
        // 本地全局配置文件写入
        await files.writeFile(settingsPath, JSON.stringify(config, null, 2));
      } else {
        await FileSystemHelper.writeFile(settingsPath, JSON.stringify(config, null, 2));
      }
      // Update the tools list to reflect the change
      const connection = this.connections.find((conn) => conn.server.name === serverName);
      if (connection) {
        connection.server.config = JSON.stringify(config.mcpServers[serverName]);
        connection.server.tools = await this.fetchToolsList(serverName);
        await this.notifyWebviewOfServerChanges();
      }
    } catch (error) {
      console.error('Failed to update autoApprove settings:', error);
      vscode.window.showErrorMessage(JoyCoderMCPMessageMap['Failed to update autoApprove settings']);
      throw error; // Re-throw to ensure the error is properly handled
    }
  }

  public async addRemoteServer(serverName: string, serverUrl: string) {
    try {
      const settings = await this.readAndValidateMcpSettingsFile();
      if (!settings) {
        throw new Error('Failed to read MCP settings');
      }

      if (settings.mcpServers[serverName]) {
        throw new Error(`An MCP server with the name "${serverName}" already exists`);
      }

      const urlValidation = z.string().url().safeParse(serverUrl);
      if (!urlValidation.success) {
        throw new Error(`Invalid server URL: ${serverUrl}. Please provide a valid URL.`);
      }

      const serverConfig = {
        url: serverUrl,
        disabled: false,
        autoApprove: [],
      };

      const parsedConfig = ServerConfigSchema.parse(serverConfig);

      settings.mcpServers[serverName] = parsedConfig;
      const settingsPath = await this.getMcpSettingsFilePath();

      // We don't write the zod-transformed version to the file.
      // The above parse() call adds the transportType field to the server config
      // It would be fine if this was written, but we don't want to clutter up the file with internal details

      // ToDo: We could benefit from input / output types reflecting the non-transformed / transformed versions
      await fs.writeFile(
        settingsPath,
        JSON.stringify({ mcpServers: { ...settings.mcpServers, [serverName]: serverConfig } }, null, 2)
      );

      await this.updateServerConnections(settings.mcpServers);

      vscode.window.showInformationMessage(`Added ${serverName} MCP server`);
    } catch (error) {
      console.error('Failed to add remote MCP server:', error);

      throw error;
    }
  }

  public async deleteServer(serverName: string, projectType?: ProjectType) {
    try {
      const isProject = projectType === 'project';

      const settingsPath = isProject
        ? await this.getMcpSettingsFilePath('initial')
        : await this.getMcpSettingsFilePath();

      let content;
      if (isProject) {
        content = await FileSystemHelper.readFile(settingsPath, 'utf-8');
      } else {
        content = await files.readFile(settingsPath, 'utf-8');
      }

      const config = JSON.parse(content);
      if (!config.mcpServers || typeof config.mcpServers !== 'object') {
        config.mcpServers = {};
      }
      if (config.mcpServers[serverName]) {
        delete config.mcpServers[serverName];
        const updatedConfig = {
          mcpServers: config.mcpServers,
        };

        if (isProject) {
          await FileSystemHelper.writeFile(settingsPath, JSON.stringify(updatedConfig, null, 2));
        } else {
          await files.writeFile(settingsPath, JSON.stringify(updatedConfig, null, 2));
        }

        const config2 = await this.readAndValidateMcpSettingsFile();
        if (config2) {
          await this.updateServerConnections(config2.mcpServers);
        }
        vscode.window.showInformationMessage(`已删除 ${serverName} MCP 服务器`);
        // vscode.window.showInformationMessage(`Deleted ${serverName} MCP server`);
        this.notifyWebviewOfServerChanges();
      } else {
        vscode.window.showWarningMessage(`在 MCP 配置中未找到 ${serverName}`);
        // vscode.window.showWarningMessage(`${serverName} not found in MCP configuration`);
      }
    } catch (error) {
      vscode.window.showErrorMessage(
        `删除MCP服务器失败： ${error instanceof Error ? error.message : String(error)}`
        // `Failed to delete MCP server: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  async dispose(): Promise<void> {
    this.removeAllFileWatchers();
    for (const connection of this.connections) {
      try {
        await this.deleteConnection(connection.server.name);
      } catch (error) {
        console.error(`Failed to close connection for ${connection.server.name}:`, error);
      }
    }
    this.connections = [];
    if (this.settingsWatcher) {
      this.settingsWatcher.dispose();
    }
    this.disposables.forEach((d) => d.dispose());
  }
}
