import * as vscode from 'vscode';
import { openFile } from '../../integrations/misc/open-file';
import { UrlContentFetcher } from '../../services/browser/UrlContentFetcherPlaywright';
import { mentionRegexGlobal } from '../../shared/context-mentions';
import { extractTextFromFile } from '../../integrations/misc/extract-text';
import { isBinaryFile } from 'isbinaryfile';
import { diagnosticsToProblemsString } from '../../integrations/diagnostics';
import { getLatestTerminalOutput } from '../../integrations/terminal/get-latest-output';
import { getCommitInfo, getWorkingState } from '../../utils/git';
import { FileContextTracker } from '../context-tracking/FileContextTracker';
// import { getFoldersAndFirstFileContent } from './getFoldersAndFirstFileContent';
import { FileSystemHelper } from '../../utils/FileSystemHelper';
import { isRemoteEnvironment } from '../../utils/fs';
import DocTracker from '../../integrations/doc/DocTracker';
import { UserContent } from '../Joycoder';
import { Base64ImageSource, ContentBlockParam } from '@anthropic-ai/sdk/resources/messages.mjs';
import { ImageUtils } from '../../utils/imageUtils';

export function openMention(mention?: string): void {
  if (!mention) {
    return;
  }

  const cwd = vscode.workspace.workspaceFolders
    ?.map((folder) => (isRemoteEnvironment() ? folder.uri : folder.uri.fsPath))
    .at(0);
  if (!cwd) {
    return;
  }

  if (mention.startsWith('/')) {
    const relPath = mention.slice(1);
    const absPath = FileSystemHelper.resolve(cwd, relPath);
    if (mention.endsWith('/')) {
      vscode.commands.executeCommand('revealInExplorer', vscode.Uri.file(absPath));
    } else {
      openFile(absPath);
    }
  } else if (mention === 'problems') {
    vscode.commands.executeCommand('workbench.actions.view.problems');
  } else if (mention.startsWith('http')) {
    vscode.env.openExternal(vscode.Uri.parse(mention));
  }
}

export async function parseMentions(
  block: ContentBlockParam,
  cwd: string | vscode.Uri,
  urlContentFetcher: UrlContentFetcher,
  fileContextTracker?: FileContextTracker,
  docTracker?: DocTracker
): Promise<UserContent> {
  const additionalContentBlocks = [] as ContentBlockParam[];
  if (block.type === 'text') {
    const text = block.text;
    const mentions: Set<string> = new Set();
    let parsedText = text.replace(mentionRegexGlobal, (match, mention) => {
      mentions.add(mention);

      if (mention.startsWith('http')) {
        return `'${mention}' (see below for site content)`;
      } else if (mention.startsWith('/')) {
        const mentionPath = mention.slice(1); // Remove the leading '/'
        return mentionPath.endsWith('/')
          ? `'${mentionPath}' (see below for folder content)`
          : `'${mentionPath}' (see below for file content)`;
      } else if (mention === 'problems') {
        return `Workspace Problems (see below for diagnostics)`;
      } else if (mention === 'terminal') {
        return `Terminal Output (see below for output)`;
      } else if (mention === 'git-changes') {
        return `Working directory changes (see below for details)`;
      } else if (/^[a-f0-9]{7,40}$/.test(mention)) {
        return `Git commit '${mention}' (see below for commit info)`;
      } else if (mention.startsWith('.joycoder/rules/')) {
        return `Rule '${mention}' (see below for rule content)`;
      } else if (mention.startsWith('doc-')) {
        return `Doc '${mention}' (see below for Doc content)`;
      }
      return match;
    });

    const urlMention = Array.from(mentions).find((mention) => mention.startsWith('http'));
    let launchBrowserError: Error | undefined;
    if (urlMention) {
      try {
        await urlContentFetcher.launchBrowser();
      } catch (error) {
        launchBrowserError = error;
        vscode.window.showErrorMessage(`Error fetching content for ${urlMention}: ${error.message}`);
      }
    }

    for (const mention of mentions) {
      if (mention.startsWith('http')) {
        let result: string;
        if (launchBrowserError) {
          result = `Error fetching content: ${launchBrowserError.message}`;
        } else {
          try {
            const markdown = await urlContentFetcher.urlToMarkdown(mention);
            result = markdown;
          } catch (error) {
            vscode.window.showErrorMessage(`Error fetching content for ${mention}: ${error.message}`);
            result = `Error fetching content: ${error.message}`;
          }
        }
        parsedText += `\n\n<url_content url="${mention}">\n${result}\n</url_content>`;
      } else if (mention.startsWith('/')) {
        const mentionPath = mention.slice(1);
        try {
          if (mention.endsWith('/')) {
            const content = await getFolderPaths(mentionPath, cwd);
            // const content = await getFileOrFolderContent(mentionPath, cwd);
            // const content = JSON.stringify(await getFoldersAndFirstFileContent(mentionPath, cwd)); // 读取所有文件夹和文件路径和第一个文件内容并返回tokens数
            parsedText += `\n\n<folder_content path="${mentionPath}">\n${content}\n</folder_content>`;
          } else {
            const absPath = FileSystemHelper.resolveUri(cwd, mentionPath);

            const isImage = ImageUtils.isValidImageSync(absPath.toString());
            const content = await getFileOrFolderContent(mentionPath, cwd);

            parsedText += isImage
              ? `\n\n<final_file_content path="${mentionPath}">\n(image file)\n</final_file_content>`
              : `\n\n<final_file_content path="${mentionPath}">\n${content}\n</final_file_content>`;
            if (isImage) {
              const media_type = ImageUtils.getImageMimeType(absPath.toString()) as Base64ImageSource['media_type'];
              const imageConent = await ImageUtils.convertToBase64(absPath.toString());
              additionalContentBlocks.push({
                type: 'image',
                source: {
                  type: 'base64',
                  data: imageConent.slice(imageConent.indexOf(',') + 1),
                  media_type,
                },
              });
            }
            // Track that this file was mentioned and its content was included
            if (fileContextTracker) {
              await fileContextTracker.trackFileContext(mentionPath, 'file_mentioned');
            }
          }
        } catch (error) {
          if (mention.endsWith('/')) {
            parsedText += `\n\n<folder_content path="${mentionPath}">\nError fetching content: ${error.message}\n</folder_content>`;
          } else {
            parsedText += `\n\n<final_file_content path="${mentionPath}">\nError fetching content: ${error.message}\n</final_file_content>`;
          }
        }
      } else if (mention === 'problems') {
        try {
          const problems = getWorkspaceProblems(cwd);
          parsedText += `\n\n<workspace_diagnostics>\n${problems}\n</workspace_diagnostics>`;
        } catch (error) {
          parsedText += `\n\n<workspace_diagnostics>\nError fetching diagnostics: ${error.message}\n</workspace_diagnostics>`;
        }
      } else if (mention === 'terminal') {
        try {
          const terminalOutput = await getLatestTerminalOutput();
          parsedText += `\n\n<terminal_output>\n${terminalOutput}\n</terminal_output>`;
        } catch (error) {
          parsedText += `\n\n<terminal_output>\nError fetching terminal output: ${error.message}\n</terminal_output>`;
        }
      } else if (mention === 'git-changes') {
        try {
          const workingState = await getWorkingState(cwd);
          parsedText += `\n\n<git_working_state>\n${workingState}\n</git_working_state>`;
        } catch (error) {
          parsedText += `\n\n<git_working_state>\nError fetching working state: ${error.message}\n</git_working_state>`;
        }
      } else if (/^[a-f0-9]{7,40}$/.test(mention)) {
        try {
          const commitInfo = await getCommitInfo(mention, cwd);
          parsedText += `\n\n<git_commit hash="${mention}">\n${commitInfo}\n</git_commit>`;
        } catch (error) {
          parsedText += `\n\n<git_commit hash="${mention}">\nError fetching commit info: ${error.message}\n</git_commit>`;
        }
      } else if (mention.startsWith('.joycoder/rules/')) {
        try {
          const content = await getFileOrFolderContent(mention, cwd);
          parsedText += `\n\n<rule_content path="${mention}">\n${content}\n</rule_content>`;
        } catch (error) {
          parsedText += `\n\n<rule_content path="${mention}">\nError fetching rule content: ${error.message}\n</rule_content>`;
        }
      } else if (mention.startsWith('doc-')) {
        try {
          const name = mention.replace('doc-', '');
          const question = extractAllTaskContents(text.replace('@doc-' + name, ''));
          const content = await docTracker?.getDocContent({ name, question });
          console.log('%c [ content ]-191', 'font-size:13px; background:pink; color:#bf2c9f;', content);
          parsedText += `\n\n<doc_content name='${name}.md'>
          # The following is the recalled content from the knowledge base, serving as background knowledge:
            \`\`\` markdown
              \n${content}\n
            \`\`\`
          </doc_content>`;
        } catch (error) {
          parsedText += `\n\n<doc_content name='${''}'>\nError fetching doc content: ${error.message}\n</doc_content>`;
        }
      }
    }

    if (urlMention) {
      try {
        await urlContentFetcher.closeBrowser();
      } catch (error) {
        console.error(`Error closing browser: ${error.message}`);
      }
    }
    block.text = parsedText;
    return [block, ...additionalContentBlocks];
  }
  return [];
}

async function getFileOrFolderContent(mentionPath: string, cwd: string | vscode.Uri): Promise<string> {
  const absPath = FileSystemHelper.resolveUri(cwd, mentionPath);
  try {
    const stats = await FileSystemHelper.stat(absPath);
    if (stats.isFile()) {
      //  选中内容问答返回数据
      const content = await extractTextFromFile(absPath);
      return content;
    } else if (stats.isDirectory()) {
      const entries = await FileSystemHelper.readdir(absPath, { withFileTypes: true });
      let folderContent = '';
      const fileContentPromises: Promise<string | undefined>[] = [];
      entries.forEach((entry: { isFile: () => any; name: string; isDirectory: () => any }, index: number) => {
        const isLast = index === entries.length - 1;
        const linePrefix = isLast ? '└── ' : '├── ';
        if (entry.isFile()) {
          folderContent += `${linePrefix}${entry.name}\n`;
          const filePath = FileSystemHelper.join(mentionPath, entry.name);
          const absoluteFilePath = FileSystemHelper.resolveUri(absPath, entry.name);
          // const relativeFilePath = path.relative(cwd, absoluteFilePath);
          fileContentPromises.push(
            (async () => {
              try {
                const isBinary = await isBinaryFile(FileSystemHelper.getRemotePath(absoluteFilePath)).catch(
                  () => false
                );
                if (isBinary) {
                  return undefined;
                }
                const content = await extractTextFromFile(absoluteFilePath);
                return `<final_file_content path="${filePath.toPosix()}">\n${content}\n</final_file_content>`;
              } catch (error) {
                return undefined;
              }
            })()
          );
        } else if (entry.isDirectory()) {
          folderContent += `${linePrefix}${entry.name}/\n`;
          // not recursively getting folder contents
        } else {
          folderContent += `${linePrefix}${entry.name}\n`;
        }
      });
      const fileContents = (await Promise.all(fileContentPromises)).filter((content) => content);
      return `${folderContent}\n${fileContents.join('\n\n')}`.trim();
    } else {
      return `(Failed to read contents of ${mentionPath})`;
    }
  } catch (error) {
    throw new Error(`Failed to access path "${mentionPath}": ${error.message}`);
  }
}

/**
 * 获取指定路径下的所有文件夹和文件地址
 * @param mentionPath 相对路径
 * @param cwd 当前工作目录
 * @param recursive 是否递归获取子文件夹，默认为 false
 * @returns 返回文件夹和文件路径数组
 */
async function getFolderPaths(
  mentionPath: string,
  cwd: string | vscode.Uri,
  recursive: boolean = false
): Promise<string[]> {
  const absPath = FileSystemHelper.resolveUri(cwd, mentionPath);
  const allPaths: string[] = [];

  try {
    const stats = await FileSystemHelper.stat(absPath);
    if (!stats.isDirectory()) {
      throw new Error(`Path "${mentionPath}" is not a directory`);
    }

    const entries = await FileSystemHelper.readdir(absPath, { withFileTypes: true });

    for (const entry of entries) {
      const entryPath = FileSystemHelper.join(mentionPath, entry.name).toPosix();

      if (entry.isDirectory()) {
        allPaths.push(entryPath);

        // 如果需要递归获取子文件夹
        if (recursive) {
          try {
            const subPaths = await getFolderPaths(entryPath, cwd, recursive);
            allPaths.push(...subPaths);
          } catch (error) {
            // 忽略无法访问的子文件夹
            console.warn(`Cannot access subfolder ${entryPath}: ${error.message}`);
          }
        }
      } else if (entry.isFile()) {
        // 添加文件路径
        allPaths.push(entryPath);
      }
    }

    return allPaths;
  } catch (error) {
    throw new Error(`Failed to access directory "${mentionPath}": ${error.message}`);
  }
}

function getWorkspaceProblems(cwd: string | vscode.Uri): string {
  const diagnostics = vscode.languages.getDiagnostics();
  const result = diagnosticsToProblemsString(
    diagnostics,
    [vscode.DiagnosticSeverity.Error, vscode.DiagnosticSeverity.Warning],
    cwd
  );
  if (!result) {
    return 'No errors or warnings detected.';
  }
  return result;
}

function extractAllTaskContents(input: string): string {
  const taskRegex = /<task>(.*?)<\/task>/s;
  const match = input.match(taskRegex);
  return match ? match[1] : '';
}
