import '../../utils/path'; // Import to ensure String.prototype.toPosix is available

import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import cloneDeep from 'clone-deep';
import { JoyCoderSayText } from '../../shared/ExtensionMessage';
import { formatResponse } from '../prompts/responses';
import { JoyCoderRestoreMessageMap } from '../../adaptor/translate/message';
import { defaultModeSlug } from '../../../web-agent/src/utils/modes';
import { JoyCoder, UserContent } from '../Joycoder';
import { validateToolUse } from '../webview/mode-validator';
import { ToolName } from '../../schemas';
import { useReplaceFileTool } from './tools/useReplaceFileTool';
import { applyDiffTool } from './tools/applyDiffTool';
import { fetchInstructionsTool } from './tools/fetchInstructionsTool';
import { insertContentTool } from './tools/insertContentTool';
import { switchModeTool } from './tools/switchModeTool';
import { useWriteFileTool } from './tools/useWriteFile';
import { toolDescription, pushToolResult } from './tools/common';
import { useReadFileTool } from './tools/useReadFileTool';
import { useWebSearchTool } from './tools/useWebSearchTool';
import { useCodeBaseTool } from './tools/useCodebaseTool';
import { newTaskCreationTool } from './tools/newTaskCreationTool';
import { useListFilesTool } from './tools/useListFilesTool';
import { useDefinitionNamesTool } from './tools/useDefinitionNamesTool';
import { useSearchFiles } from './tools/useSearchFiles';
import { useBrowserTool } from './tools/useBrowserTool';
import { useCommandTool } from './tools/useCommandTool';
import { useMcpTools } from './tools/useMcpTools';
import { getMcpResourceTool } from './tools/getMcpResourceTool';
import { getUserQuestionTool } from './tools/getUserQuestionTool';
import { codenseTool } from './tools/codenseTool';
import { getMcpInstructionsTool } from './tools/getMcpInstructionsTool';
import { attemptTaskDoneTool } from './tools/attemptTaskDoneTool';
import { newTaskWithContextTool } from './tools/newTaskWithContextTool';
import { useClearPublishTool } from './tools/useClearPublishTool';
import { updateTodoListTool } from './tools/updateTodoListTool';

/**
 * 过滤结果文本，移除HTML打开命令
 */
function filterHtmlCommandsFromText(text: string): string {
  console.log(`[JoyCoder DEBUG] filterHtmlCommandsFromText called with: "${text}"`);

  // 移除 <command>open xxx.html</command> 或 <command>start xxx.html</command> 标签
  // 支持多种格式：<command>open file.html</command>, <command>start 'file.html'</command> 等
  const commandTagPattern = /<command[^>]*>\s*(open|start)\s+(['"]?[^\s'"]*\.html['"]?)\s*<\/command>/gi;
  console.log(`[JoyCoder DEBUG] Command tag filter pattern: ${commandTagPattern}`);

  let filteredText = text.replace(commandTagPattern, '').trim();
  console.log(`[JoyCoder DEBUG] After command tag filtering: "${filteredText}"`);

  // 也处理没有command标签的情况（向后兼容）
  const plainCommandPattern = /^.*\b(open|start)\s+(['"]?[^\s'"]*\.html['"]?)\b.*$/gim;
  filteredText = filteredText.replace(plainCommandPattern, '').trim();
  console.log(`[JoyCoder DEBUG] After plain command filtering: "${filteredText}"`);

  // 清理多余的空行
  const finalText = filteredText.replace(/\n\s*\n\s*\n/g, '\n\n');

  return finalText;
}

// 用于防止重复处理同一个HTML文件的Set
const processedHtmlFiles = new Set<string>();

// 用于累积流式输出文本的全局变量
let accumulatedText = '';

/**
 * 重置HTML命令检测的状态
 */
export function resetHtmlCommandDetection() {
  accumulatedText = '';
  processedHtmlFiles.clear();
}

/**
 * 自动检测文本中的HTML打开命令并拦截处理
 */
async function autoOpenHtmlFilesFromText(joyCoder: JoyCoder, text: string): Promise<void> {
  try {
    console.log(`[JoyCoder DEBUG] autoOpenHtmlFilesFromText called with text: "${text}"`);

    // 首先检测 <command>open xxx.html</command> 格式
    const commandTagPattern = /<command[^>]*>\s*(open|start)\s+(['"]?)([^'"\s]*\.html)\2\s*<\/command>/gi;
    console.log(`[JoyCoder DEBUG] Using command tag pattern: ${commandTagPattern}`);

    let matches = [...text.matchAll(commandTagPattern)];
    console.log(`[JoyCoder DEBUG] Found ${matches.length} command tag matches:`, matches);

    // 如果没有找到command标签格式，尝试普通格式（向后兼容）
    if (matches.length === 0) {
      const plainCommandPattern = /\b(open|start)\s+(['"]?)([^'"\s]*\.html)\2/gi;
      console.log(`[JoyCoder DEBUG] Using plain command pattern: ${plainCommandPattern}`);
      matches = [...text.matchAll(plainCommandPattern)];
      console.log(`[JoyCoder DEBUG] Found ${matches.length} plain command matches:`, matches);
    }

    if (!matches || matches.length === 0) {
      // 检查是否包含关键词
      const hasOpen = text.toLowerCase().includes('open');
      const hasStart = text.toLowerCase().includes('start');
      const hasHtml = text.toLowerCase().includes('html');
      console.log(`[JoyCoder DEBUG] Keywords found - open: ${hasOpen}, start: ${hasStart}, html: ${hasHtml}`);

      if (hasHtml || hasOpen || hasStart) {
        console.log(`[JoyCoder DEBUG] No HTML commands found but keywords present in text:`, text);
      }
      return;
    }

    console.log(`[JoyCoder] Detected ${matches.length} HTML open/start command(s) in streaming text`);
    console.log(`[JoyCoder] Full text being analyzed:`, text);

    // 处理每个匹配到的命令
    for (const match of matches) {
      const command = match[1]; // 'open' 或 'start'
      const quote = match[2]; // 引号类型 (', ", 或空)
      const htmlFile = match[3]; // HTML文件路径（不包含引号）

      console.log(`[JoyCoder] Processing ${command} command for file: ${htmlFile} (quote: ${quote || 'none'})`);

      try {
        // 构建完整路径
        let fullPath = htmlFile;
        if (!path.isAbsolute(htmlFile)) {
          const cwdPath = typeof joyCoder.cwd === 'string' ? joyCoder.cwd : joyCoder.cwd.fsPath;
          fullPath = path.resolve(cwdPath, htmlFile);
        }

        // 检查是否已经处理过这个文件
        if (processedHtmlFiles.has(fullPath)) {
          console.log(`[JoyCoder] HTML file already processed: ${fullPath}`);
          continue;
        }

        // 检查文件是否存在
        if (!fs.existsSync(fullPath)) {
          console.warn(`[JoyCoder] HTML file does not exist: ${fullPath}`);
          continue; // 跳过不存在的文件，尝试下一个
        }

        console.log(`[JoyCoder] Opening HTML file in browser: ${fullPath}`);

        // 使用vscode命令打开内置浏览器
        const fileUri = vscode.Uri.file(fullPath);
        await vscode.commands.executeCommand('JoyCode.browser.open', fileUri.toString());

        console.log(`[JoyCoder] Successfully opened ${htmlFile} in internal browser`);

        // 标记为已处理
        processedHtmlFiles.add(fullPath);

        // 只处理第一个命令，避免打开太多窗口
        break;
      } catch (error) {
        console.warn(`[JoyCoder] Primary method failed for ${htmlFile}, trying fallback:`, error);

        // 尝试备用方法
        try {
          const { openInVscodeWithBrowser } = await import('@joycoder/plugin-base-browser');
          const fileUri = vscode.Uri.file(
            path.isAbsolute(htmlFile)
              ? htmlFile
              : path.resolve(typeof joyCoder.cwd === 'string' ? joyCoder.cwd : joyCoder.cwd.fsPath, htmlFile)
          );
          openInVscodeWithBrowser(fileUri.toString());
          console.log(`[JoyCoder] Successfully opened ${htmlFile} using fallback method`);
          break;
        } catch (fallbackError) {
          console.error(`[JoyCoder] All methods failed for ${htmlFile}:`, fallbackError);
        }
      }
    }
  } catch (error) {
    console.error('[JoyCoder] Error in autoOpenHtmlFilesFromText:', error);
  }
}

export async function presentAssistantMessage(
  jc: JoyCoder,
  conversationId: string,
  cwd: string | vscode.Uri,
  userContent: UserContent
) {
  if (jc.abort) {
    throw new Error(JoyCoderRestoreMessageMap['JoyCode instance aborted']);
  }

  if (jc.presentAssistantMessageLocked) {
    jc.presentAssistantMessageHasPendingUpdates = true;
    return;
  }
  jc.presentAssistantMessageLocked = true;
  jc.presentAssistantMessageHasPendingUpdates = false;

  // 如果是第一个内容块，重置累积状态
  if (jc.currentStreamingContentIndex === 0) {
    console.log(`[JoyCoder DEBUG] Starting new message, resetting accumulated text`);
    accumulatedText = '';
  }

  if (jc.currentStreamingContentIndex >= jc.assistantMessageContent.length) {
    // this may happen if the last content block was completed before streaming could finish. if streaming is finished, and we're out of bounds then this means we already presented/executed the last content block and are ready to continue to next request
    if (jc.didCompleteReadingStream) {
      jc.userMessageContentReady = true;
    }
    // console.log("no more content blocks to stream! this shouldn't happen?")
    jc.presentAssistantMessageLocked = false;
    return;
    //throw new Error("No more content blocks to stream! this shouldn't happen...") // remove and just return after testing
  }

  const block = cloneDeep(jc.assistantMessageContent[jc.currentStreamingContentIndex]); // need to create copy bc while stream is updating the array, it could be updating the reference block properties too
  switch (block.type) {
    case 'text': {
      console.log(`[JoyCoder DEBUG] Processing text block - partial: ${block.partial}, content: "${block.content}"`);

      if (jc.didRejectTool || jc.didAlreadyUseTool) {
        console.log(`[JoyCoder DEBUG] Skipping text block due to tool rejection or already used tool`);
        break;
      }
      let content = block.content;
      if (content) {
        // Remove partial XML tag at the very end of the content (for tool use and thinking tags)
        // (prevents scrollview from jumping when tags are automatically removed)
        const lastOpenBracketIndex = content.lastIndexOf('<');
        if (lastOpenBracketIndex !== -1) {
          const possibleTag = content.slice(lastOpenBracketIndex);
          // Check if there's a '>' after the last '<' (i.e., if the tag is complete) (complete thinking and tool tags will have been removed by now)
          const hasCloseBracket = possibleTag.includes('>');
          if (!hasCloseBracket) {
            // Extract the potential tag name
            let tagContent: string = '';
            if (possibleTag.startsWith('</')) {
              tagContent = possibleTag.slice(2).trim();
            } else {
              tagContent = possibleTag.slice(1).trim();
            }
            // Check if tagContent is likely an incomplete tag name (letters and underscores only)
            const isLikelyTagName = /^[a-zA-Z_]+$/.test(tagContent);
            // Preemptively remove < or </ to keep from these artifacts showing up in chat (also handles closing thinking tags)
            const isOpeningOrClosing = possibleTag === '<' || possibleTag === '</';
            // If the tag is incomplete and at the end, remove it from the content
            if (isOpeningOrClosing || isLikelyTagName) {
              content = content.slice(0, lastOpenBracketIndex).trim();
            }
          }
        }
      }

      // 累积文本内容用于检测跨块的HTML命令
      if (content) {
        accumulatedText += content;

        // 添加详细调试信息
        console.log(`[JoyCoder DEBUG] Block partial: ${block.partial}, Content: "${content}"`);
        console.log(`[JoyCoder DEBUG] Accumulated text: "${accumulatedText}"`);

        // 简单检测：检查是否包含command标签或HTML相关内容
        if (content.includes('<command>') || (content.includes('open') && content.includes('.html'))) {
          console.log(
            `[JoyCoder DEBUG] POTENTIAL HTML COMMAND: Found command tag or HTML-related content in current block!`
          );
        }

        // 累积检测：在累积文本中查找
        if (
          accumulatedText.includes('<command>') ||
          (accumulatedText.includes('open') && accumulatedText.includes('.html'))
        ) {
          console.log(
            `[JoyCoder DEBUG] POTENTIAL HTML COMMAND: Found command tag or HTML-related content in accumulated text!`
          );
        }

        // 检测并处理HTML命令（使用累积的文本）
        await autoOpenHtmlFilesFromText(jc, accumulatedText);

        // 如果是完整消息，重置累积文本
        if (!block.partial) {
          console.log(`[JoyCoder DEBUG] Resetting accumulated text (was: "${accumulatedText}")`);
          accumulatedText = '';
        }
      }

      if (!block.partial) {
        // Some models add code block artifacts (around the tool calls) which show up at the end of text content
        // matches ``` with at least one char after the last backtick, at the end of the string
        const match = content?.trimEnd().match(/```[a-zA-Z0-9_-]+$/);
        if (match) {
          const matchLength = match[0].length;
          content = content.trimEnd().slice(0, -matchLength);
        }
      }

      // 过滤HTML打开命令，避免显示在对话中
      if (content) {
        const originalContent = content;
        content = filterHtmlCommandsFromText(content);
        if (originalContent !== content) {
          console.log(`[JoyCoder DEBUG] Content filtered - Original: "${originalContent}", Filtered: "${content}"`);
        }
      }

      await jc.say(
        'text',
        JSON.stringify({
          text: content,
          conversationId: jc.conversationId,
          taskId: jc.taskId,
          sessionId: jc.sessionId,
          userContent: block.userContent,
        } as JoyCoderSayText),
        undefined,
        block.partial
      );
      break;
    }
    case 'tool_use':
      if (jc.didRejectTool) {
        // ignore any tool content after user has rejected tool once
        if (!block.partial) {
          jc.userMessageContent.push({
            type: 'text',
            text: `Skipping tool ${await toolDescription(block, jc)} due to user rejecting a previous tool.`,
          });
        } else {
          // partial tool after user rejected a previous tool
          jc.userMessageContent.push({
            type: 'text',
            text: `Tool ${await toolDescription(
              block,
              jc
            )} was interrupted and not executed due to user rejecting a previous tool.`,
          });
        }
        break;
      }

      if (jc.didAlreadyUseTool) {
        // ignore any content after a tool has already been used
        jc.userMessageContent.push({
          type: 'text',
          text: `Tool [${block.name}] was not executed because a tool has already been used in this message. Only one tool may be used per message. You must assess the first tool's result before proceeding to use the next tool.`,
        });
        break;
      }

      if (block.name !== 'use_browser') {
        await jc.browserSession.closeBrowser();
      }

      // Validate tool use before execution
      const { mode, customModes } = (await jc.providerRef.deref()?.getState()) ?? {};
      try {
        validateToolUse(
          block.name as ToolName,
          mode ?? defaultModeSlug,
          customModes ?? [],
          {
            apply_diff: true,
          },
          block.params
        );
      } catch (error) {
        jc.consecutiveMistakeCount++;
        await pushToolResult(jc, block, formatResponse.toolError(error.message));
        break;
      }

      switch (block.name) {
        case 'apply_diff':
          await applyDiffTool(jc, block);
          break;
        case 'insert_content':
          await insertContentTool(jc, block);
          break;
        case 'fetch_instructions':
          await fetchInstructionsTool(jc, block);
          break;
        case 'switch_mode':
          await switchModeTool(jc, block);
          break;
        case 'use_search_and_replace':
          await useReplaceFileTool(jc, block);
          break;
        case 'use_write_file':
          await useWriteFileTool(jc, block);
          break;
        case 'use_read_file':
          await useReadFileTool(jc, block);
          break;
        case 'use_web_search':
          await useWebSearchTool(jc, block);
          break;
        case 'use_codebase':
          await useCodeBaseTool(jc, block);
          break;
        case 'use_clear_publish':
          await useClearPublishTool(jc, block);
          break;
        case 'new_task_creation':
          await newTaskCreationTool(jc, block);
          break;
        case 'new_task_with_condense_context':
          await newTaskWithContextTool(jc, block);
          break;
        case 'use_list_files':
          await useListFilesTool(jc, block);
          break;
        case 'use_definition_names':
          await useDefinitionNamesTool(jc, block);
          break;
        case 'use_search_files':
          await useSearchFiles(jc, block);
          break;
        case 'use_browser':
          await useBrowserTool(jc, block);
          break;
        case 'use_command':
          await useCommandTool(jc, block);
          break;
        case 'use_mcp_tools':
          await useMcpTools(jc, block);
          break;
        case 'get_mcp_resource':
          await getMcpResourceTool(jc, block);
          break;
        case 'get_user_question':
          await getUserQuestionTool(jc, block);
          break;
        case 'condense':
          await codenseTool(jc, block);
          break;
        case 'get_mcp_instructions':
          await getMcpInstructionsTool(jc, block);
          break;
        case 'attempt_task_done':
          await attemptTaskDoneTool(jc, block);
          break;
        case 'update_todo_list':
          await updateTodoListTool(jc, block);
      }
      break;
  }

  const recentlyModifiedFiles = jc.fileContextTracker.getAndClearRecentlyModifiedFiles();

  if (recentlyModifiedFiles.length > 0) {
    // TODO: We can track what file changes were made and only
    // checkpoint those files, this will be save storage.
    // await jc.saveCheckpoint();
  }
  /*
		Seeing out of bounds is fine, it means that the next too call is being built up and ready to add to assistantMessageContent to present.
		When you see the UI inactive during jc, it means that a tool is breaking without presenting any UI. For example the use_write_file tool was breaking when relpath was undefined, and for invalid relpath it never presented UI.
		*/
  jc.presentAssistantMessageLocked = false; // this needs to be placed here, if not then calling jc.presentAssistantMessage below would fail (sometimes) since it's locked
  // NOTE: when tool is rejected, iterator stream is interrupted and it waits for userMessageContentReady to be true. Future calls to present will skip execution since didRejectTool and iterate until contentIndex is set to message length and it sets userMessageContentReady to true itself (instead of preemptively doing it in iterator)
  if (!block.partial || jc.didRejectTool || jc.didAlreadyUseTool) {
    // block is finished streaming and executing
    if (jc.currentStreamingContentIndex === jc.assistantMessageContent.length - 1) {
      // its okay that we increment if !didCompleteReadingStream, it'll just return bc out of bounds and as streaming continues it will call presentAssistantMessage if a new block is ready. if streaming is finished then we set userMessageContentReady to true when out of bounds. this gracefully allows the stream to continue on and all potential content blocks be presented.
      // last block is complete and it is finished executing
      jc.userMessageContentReady = true; // will allow pwaitfor to continue
    }

    // call next block if it exists (if not then read stream will call it when its ready)
    jc.currentStreamingContentIndex++; // need to increment regardless, so when read stream calls this function again it will be streaming the next block

    if (jc.currentStreamingContentIndex < jc.assistantMessageContent.length) {
      // there are already more content blocks to stream, so we'll call this function ourselves
      // await jc.presentAssistantContent()

      presentAssistantMessage(jc, conversationId, cwd, userContent);
      return;
    }
  }
  // block is partial, but the read stream may have finished
  if (jc.presentAssistantMessageHasPendingUpdates) {
    presentAssistantMessage(jc, conversationId, cwd, userContent);
  }
}
