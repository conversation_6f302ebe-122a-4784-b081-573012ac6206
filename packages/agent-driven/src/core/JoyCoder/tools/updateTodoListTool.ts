import cloneDeep from 'clone-deep';
import crypto from 'crypto';
import { JoyCoder } from '../../Joycoder';
import { TodoItem, TodoStatus, todoStatusSchema } from '../../../shared/todo';
import { AskApproval, <PERSON>leError, PushToolResult, RemoveClosingTag } from '../../../shared/tools';
import { ToolUse } from '../../assistant-message';
import { formatResponse } from '../../prompts/responses';
import { JoyCoderMessage } from '../../../shared/ExtensionMessage';
import { pushToolResult, askApproval, handleError } from './common';

let approvedTodoList: TodoItem[] | undefined = undefined;
export function getLatestTodo(joycoderMessages: JoyCoderMessage[]) {
  const todos = joycoderMessages
    .filter(
      (msg) => (msg.type === 'ask' && msg.ask === 'tool') || (msg.type === 'say' && msg.say === 'user_edit_todos')
    )
    .map((msg) => {
      try {
        return JSON.parse(msg.text ?? '{}');
      } catch {
        return null;
      }
    })
    .filter((item) => item && item.tool === 'updateTodoList' && Array.isArray(item.todos))
    .map((item) => item.todos)
    .pop();
  if (todos) {
    return todos;
  } else {
    return [];
  }
}

/**
 * Add a todo item to the task's todoList.
 */
export function addTodoToTask(
  joycode: JoyCoder,
  content: string,
  status: TodoStatus = 'pending',
  id?: string
): TodoItem {
  const todo: TodoItem = {
    id: id ?? crypto.randomUUID(),
    content,
    status,
  };
  if (!joycode.todoList) joycode.todoList = [];
  joycode.todoList.push(todo);
  return todo;
}

/**
 * Update the status of a todo item by id.
 */
export function updateTodoStatusForTask(joycode: JoyCoder, id: string, nextStatus: TodoStatus): boolean {
  if (!joycode.todoList) return false;
  const idx = joycode.todoList.findIndex((t) => t.id === id);
  if (idx === -1) return false;
  const current = joycode.todoList[idx];
  if (
    (current.status === 'pending' && nextStatus === 'in_progress') ||
    (current.status === 'in_progress' && nextStatus === 'completed') ||
    current.status === nextStatus
  ) {
    joycode.todoList[idx] = { ...current, status: nextStatus };
    return true;
  }
  return false;
}

/**
 * Remove a todo item by id.
 */
export function removeTodoFromTask(joycode: JoyCoder, id: string): boolean {
  if (!joycode.todoList) return false;
  const idx = joycode.todoList.findIndex((t) => t.id === id);
  if (idx === -1) return false;
  joycode.todoList.splice(idx, 1);
  return true;
}

/**
 * Get a copy of the todoList.
 */
export function getTodoListForTask(joycode: JoyCoder): TodoItem[] | undefined {
  return joycode.todoList?.slice();
}

/**
 * Set the todoList for the task.
 */
export async function setTodoListForTask(joycode?: JoyCoder, todos?: TodoItem[]) {
  if (joycode === undefined) return;
  joycode.todoList = Array.isArray(todos) ? todos : [];
}

/**
 * Restore the todoList from argument or from joycodeMessages.
 */
export function restoreTodoListForTask(joycode: JoyCoder, todoList?: TodoItem[]) {
  if (todoList) {
    joycode.todoList = Array.isArray(todoList) ? todoList : [];
    return;
  }
  joycode.todoList = getLatestTodo(joycode.JoyCoderMessages);
}
/**
 * Convert TodoItem[] to markdown checklist string.
 * @param todos TodoItem array
 * @returns markdown checklist string
 */
function todoListToMarkdown(todos: TodoItem[]): string {
  return todos
    .map((t) => {
      let box = '[ ]';
      if (t.status === 'completed') box = '[x]';
      else if (t.status === 'in_progress') box = '[-]';
      return `${box} ${t.content}`;
    })
    .join('\n');
}

function normalizeStatus(status: string | undefined): TodoStatus {
  if (status === 'completed') return 'completed';
  if (status === 'in_progress') return 'in_progress';
  return 'pending';
}

function parseMarkdownChecklist(md: string): TodoItem[] {
  if (typeof md !== 'string') return [];
  const lines = md
    .split(/\r?\n/)
    .map((l) => l.trim())
    .filter(Boolean);
  const todos: TodoItem[] = [];
  for (const line of lines) {
    const match = line.match(/^\[\s*([ xX\-~])\s*\]\s+(.+)$/);
    if (!match) continue;
    let status: TodoStatus = 'pending';
    if (match[1] === 'x' || match[1] === 'X') status = 'completed';
    else if (match[1] === '-' || match[1] === '~') status = 'in_progress';
    const id = crypto
      .createHash('md5')
      .update(match[2] + status)
      .digest('hex');
    todos.push({
      id,
      content: match[2],
      status,
    });
  }
  return todos;
}

export function setPendingTodoList(todos: TodoItem[]) {
  approvedTodoList = todos;
}

function validateTodos(todos: any[]): { valid: boolean; error?: string } {
  if (!Array.isArray(todos)) return { valid: false, error: 'todos must be an array' };
  for (const [i, t] of todos.entries()) {
    if (!t || typeof t !== 'object') return { valid: false, error: `Item ${i + 1} is not an object` };
    if (!t.id || typeof t.id !== 'string') return { valid: false, error: `Item ${i + 1} is missing id` };
    if (!t.content || typeof t.content !== 'string') return { valid: false, error: `Item ${i + 1} is missing content` };
    if (t.status && !todoStatusSchema.options.includes(t.status as TodoStatus))
      return { valid: false, error: `Item ${i + 1} has invalid status` };
  }
  return { valid: true };
}

/**
 * Update the todo list for a task.
 * @param joycode JoyCoder instance
 * @param block ToolUse block
 * @param askApproval AskApproval function
 * @param handleError HandleError function
 * @param pushToolResult PushToolResult function
 * @param removeClosingTag RemoveClosingTag function
 * @param userEdited If true, only show "User Edit Succeeded" and do nothing else
 */
export async function updateTodoListTool(joycode: JoyCoder, block: ToolUse, userEdited?: boolean) {
  console.log('%c [ updateTodoListTool ]-183', 'font-size:13px; background:pink; color:#bf2c9f;');
  // If userEdited is true, only show "User Edit Succeeded" and do nothing else
  if (userEdited === true) {
    pushToolResult(joycode, block, 'User Edit Succeeded');
    return;
  }
  try {
    const todosRaw = block.params.todos;

    let todos: TodoItem[];
    try {
      todos = parseMarkdownChecklist(todosRaw || '');
    } catch {
      joycode.consecutiveMistakeCount++;
      pushToolResult(
        joycode,
        block,
        formatResponse.toolError('The todos parameter is not valid markdown checklist or JSON')
      );
      return;
    }

    const { valid, error } = validateTodos(todos);
    if (!valid && !block.partial) {
      joycode.consecutiveMistakeCount++;
      pushToolResult(joycode, block, formatResponse.toolError(error || 'todos parameter validation failed'));
      return;
    }

    let normalizedTodos: TodoItem[] = todos.map((t) => ({
      id: t.id,
      content: t.content,
      status: normalizeStatus(t.status),
    }));

    const approvalMsg = JSON.stringify({
      tool: 'updateTodoList',
      todos: normalizedTodos,
    });
    if (block.partial) {
      await joycode.ask('tool', approvalMsg, block.partial).catch(() => {});
      return;
    }
    approvedTodoList = cloneDeep(normalizedTodos);
    let didApprove = true;
    if (!joycode.shouldAutoApproveTool(block.name)) {
      didApprove = await askApproval(joycode, block, 'tool', approvalMsg);
      if (!didApprove) {
        pushToolResult(joycode, block, 'User dejoycoded to update the todoList.');
        return;
      }
    }
    const isTodoListChanged =
      approvedTodoList !== undefined && JSON.stringify(normalizedTodos) !== JSON.stringify(approvedTodoList);
    if (isTodoListChanged) {
      normalizedTodos = approvedTodoList ?? [];
      joycode.say(
        'user_edit_todos',
        JSON.stringify({
          tool: 'updateTodoList',
          todos: normalizedTodos,
        })
      );
    }

    await setTodoListForTask(joycode, normalizedTodos);

    // If todo list changed, output new todo list in markdown format
    if (isTodoListChanged) {
      const md = todoListToMarkdown(normalizedTodos);
      pushToolResult(joycode, block, formatResponse.toolResult('User edits todo:\n\n' + md));
    } else {
      pushToolResult(joycode, block, formatResponse.toolResult('Todo list updated successfully.'));
    }
  } catch (error) {
    await handleError(joycode, block, 'update_todo_list', error);
  }
}
