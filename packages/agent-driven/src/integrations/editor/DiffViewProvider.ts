import * as vscode from 'vscode';
import * as path from 'path';
// import * as os from 'os';
// import * as files from 'fs-extra';
import { createDirectoriesForFile, isRemoteEnvironment } from '../../utils/fs';
import { arePathsEqual } from '../../utils/path';
import { formatResponse } from '../../core/prompts/responses';
import { DecorationController } from './DecorationController';
import * as diff from 'diff';
import { diagnosticsToProblemsString, getNewDiagnostics } from '../diagnostics';
import { JoyCoderEditorMessageMap } from '../../adaptor/translate/message';
import { Uri } from 'vscode';
import { FileSystemHelper } from '../../utils/FileSystemHelper';
import stripBom from 'strip-bom';
import { ImageUtils } from '../../utils/imageUtils';
import { GlobalState } from '@joycoder/shared';

export const DIFF_VIEW_URI_SCHEME = 'joycoder-diff';

/**
 * 检测是否是 memory.md 文件的绝对路径
 * @param filePath 文件路径
 * @returns 是否是 memory.md 文件
 */
// function isMemoryFile(filePath: string): boolean {
//   const memoryPath = path.join(os.homedir(), '.joycode', 'memory', 'memory.md');
//   return path.resolve(filePath) === path.resolve(memoryPath);
// }

export class DiffViewProvider {
  editType?: 'create' | 'modify';
  isEditing = false;
  originalContent: string | undefined;
  private createdDirs: string[] = [];
  private documentWasOpen = false;
  private relPath?: string;
  private newContent?: string;
  private activeDiffEditor?: vscode.TextEditor;
  private fadedOverlayController?: DecorationController;
  private activeLineController?: DecorationController;
  private streamedLines: string[] = [];
  private preDiagnostics: [vscode.Uri, vscode.Diagnostic[]][] = [];
  private hasScrolledToFirstDiff = false;
  private isSilentMode: boolean = true; // 静默模式标志

  constructor(private cwd: string | Uri) {}

  /**
   * 静默模式下的保存逻辑 - 通过VSCode文档API触发事件
   */
  private async saveSilently(absolutePath: vscode.Uri | string): Promise<{
    newProblemsMessage: string | undefined;
    userEdits: string | undefined;
    autoFormattingEdits: string | undefined;
    finalContent: string | undefined;
  }> {
    if (!this.relPath || !this.newContent) {
      return {
        newProblemsMessage: undefined,
        userEdits: undefined,
        autoFormattingEdits: undefined,
        finalContent: undefined,
      };
    }

    try {
      // 特殊处理 memory.md 文件 - 直接写入文件系统
      // if (isMemoryFile(this.relPath)) {
      //   await files.ensureDir(path.dirname(this.relPath));
      //   await files.writeFile(this.relPath, this.newContent);
      // } else {
      // 通过VSCode文档API更新内容，触发onDidChangeTextDocument事件
      await this.updateDocumentSilently(absolutePath);
      // }

      // 获取诊断信息
      const postDiagnostics = vscode.languages.getDiagnostics();
      const newProblems = diagnosticsToProblemsString(
        getNewDiagnostics(this.preDiagnostics, postDiagnostics),
        [vscode.DiagnosticSeverity.Error],
        FileSystemHelper.getRemotePath(this.cwd)
      );
      const newProblemsMessage =
        newProblems.length > 0 ? `\n\nNew problems detected after saving the file:\n${newProblems}` : '';

      return {
        newProblemsMessage,
        userEdits: undefined,
        autoFormattingEdits: undefined,
        finalContent: this.newContent,
      };
    } catch (error) {
      console.error('静默保存失败:', error);
      throw error;
    }
  }

  /**
   * 通过VSCode文档API静默更新文档内容，触发相关事件
   */
  private async updateDocumentSilently(absolutePath: vscode.Uri | string): Promise<void> {
    const uri = typeof absolutePath === 'string' ? vscode.Uri.file(absolutePath) : absolutePath;

    try {
      // 1. 打开文档（如果尚未打开）
      const document = await vscode.workspace.openTextDocument(uri);

      // 2. 检查内容是否需要更新
      const currentContent = document.getText();
      if (currentContent === this.newContent) {
        return; // 内容相同，无需更新
      }

      // 3. 通过WorkspaceEdit更新文档内容，这会触发onDidChangeTextDocument事件
      const edit = new vscode.WorkspaceEdit();
      const fullRange = new vscode.Range(document.positionAt(0), document.positionAt(currentContent.length));
      edit.replace(uri, fullRange, this.newContent!);

      // 4. 应用编辑
      const success = await vscode.workspace.applyEdit(edit);
      if (!success) {
        throw new Error('Failed to apply workspace edit');
      }

      // 5. 保存文档
      if (document.isDirty) {
        await document.save();
      }
    } catch (error) {
      console.error('通过文档API更新失败，回退到直接文件写入:', error);
      // 回退到直接文件系统写入
      await FileSystemHelper.writeFile(uri, new TextEncoder().encode(this.newContent));
    }
  }

  /**
   * 静默模式下的回滚逻辑 - 通过VSCode文档API触发事件
   */
  private async revertSilently(): Promise<void> {
    if (!this.relPath) {
      return;
    }

    const fileExists = this.editType === 'modify';

    if (!fileExists) {
      // 新文件：删除文件和目录
      await this.deleteFileSilently();
    } else {
      // 现有文件：恢复原始内容
      await this.revertFileContentSilently();
    }

    // 重置状态
    await this.reset();
  }

  /**
   * 静默删除新创建的文件
   */
  private async deleteFileSilently(): Promise<void> {
    try {
      const absePath = FileSystemHelper.resolveUri(this.cwd, this.relPath!);
      const uri = typeof absePath === 'string' ? vscode.Uri.file(absePath) : absePath;

      // 1. 检查文档是否已打开，如果是则关闭
      const openDocument = vscode.workspace.textDocuments.find((doc) => doc.uri.toString() === uri.toString());

      if (openDocument) {
        // 通过WorkspaceEdit删除文档内容，触发事件
        const edit = new vscode.WorkspaceEdit();
        edit.deleteFile(uri, { ignoreIfNotExists: true });
        await vscode.workspace.applyEdit(edit);
      } else {
        // 直接删除文件
        await FileSystemHelper.unlink(absePath);
      }

      // 删除创建的目录
      for (let i = this.createdDirs.length - 1; i >= 0; i--) {
        await FileSystemHelper.rmdir(this.createdDirs[i]);
      }
    } catch (error) {
      console.error('删除文件失败:', error);
    }
  }

  /**
   * 静默恢复文件内容
   */
  private async revertFileContentSilently(): Promise<void> {
    try {
      // if (isMemoryFile(this.relPath!)) {
      //   // memory.md 文件直接写入文件系统
      //   await files.ensureDir(path.dirname(this.relPath!));
      //   await files.writeFile(this.relPath!, this.originalContent ?? '');
      // } else {
      // 通过VSCode文档API恢复内容，触发事件
      const absePath = FileSystemHelper.resolveUri(this.cwd, this.relPath!);
      await this.updateDocumentContentSilently(absePath, this.originalContent ?? '');
      // }
    } catch (error) {
      console.error('恢复文件失败:', error);
    }
  }

  /**
   * 通过VSCode文档API更新文档内容到指定内容
   */
  private async updateDocumentContentSilently(absolutePath: vscode.Uri | string, content: string): Promise<void> {
    const uri = typeof absolutePath === 'string' ? vscode.Uri.file(absolutePath) : absolutePath;

    try {
      // 1. 打开文档（如果尚未打开）
      const document = await vscode.workspace.openTextDocument(uri);

      // 2. 检查内容是否需要更新
      const currentContent = document.getText();
      if (currentContent === content) {
        return; // 内容相同，无需更新
      }

      // 3. 通过WorkspaceEdit更新文档内容，这会触发onDidChangeTextDocument事件
      const edit = new vscode.WorkspaceEdit();
      const fullRange = new vscode.Range(document.positionAt(0), document.positionAt(currentContent.length));
      edit.replace(uri, fullRange, content);

      // 4. 应用编辑
      const success = await vscode.workspace.applyEdit(edit);
      if (!success) {
        throw new Error('Failed to apply workspace edit for revert');
      }

      // 5. 保存文档
      if (document.isDirty) {
        await document.save();
      }
    } catch (error) {
      console.error('通过文档API恢复失败，回退到直接文件写入:', error);
      // 回退到直接文件系统写入
      await FileSystemHelper.writeFile(uri, new TextEncoder().encode(content));
    }
  }

  async open(relPath: string): Promise<void> {
    const absolutePath = FileSystemHelper.resolveUri(this.cwd, relPath);
    // 判断是否为 VS不可编辑的图片
    if (ImageUtils.isValidImageSync(FileSystemHelper.getRemotePath(absolutePath))) return;
    this.relPath = relPath;
    const fileExists = this.editType === 'modify';
    this.isEditing = true;

    // 检查是否启用静默模式 - 从GlobalState获取，默认开启
    const isSilentMode = GlobalState.get('isSilentMode');
    this.isSilentMode = isSilentMode === undefined ? true : isSilentMode;
    // 如果文件已经打开，确保在获取其内容之前它不是脏的（未保存的更改）
    if (fileExists) {
      const existingDocument = vscode.workspace.textDocuments.find((doc) =>
        arePathsEqual(doc.uri.fsPath, FileSystemHelper.getRemotePath(absolutePath))
      );

      if (existingDocument && existingDocument.isDirty) {
        await existingDocument.save();
      }
    }

    // 在编辑文件之前获取诊断信息，我们将在编辑后与之比较，以查看joycoder是否需要修复任何问题
    this.preDiagnostics = vscode.languages.getDiagnostics();

    if (fileExists) {
      // 特殊处理 memory.md 文件，使用本地文件系统操作
      // if (isMemoryFile(relPath)) {
      //   this.originalContent = await files.readFile(relPath, 'utf-8');
      // } else {
      this.originalContent = await FileSystemHelper.readFile(absolutePath, 'utf-8');
      // }
    } else {
      this.originalContent = '';
    }
    // 对于新文件，创建必要的目录并跟踪新创建的目录，以便在用户拒绝操作时删除它们
    this.createdDirs = await createDirectoriesForFile(absolutePath);
    // 确保在打开文件之前文件存在
    if (!fileExists) {
      // 特殊处理 memory.md 文件，使用本地文件系统操作
      // if (isMemoryFile(relPath)) {
      //   await files.ensureDir(path.dirname(relPath));
      //   await files.writeFile(relPath, '');
      // } else {
      // 创建文件
      await FileSystemHelper.writeFile(absolutePath, new TextEncoder().encode(''));
      // }
    }
    // if the file was already open, close it (must happen after showing the diff view since if it's the only tab the column will close)
    this.documentWasOpen = false;
    // close the tab if it's open (it's already saved above)
    const tabs = vscode.window.tabGroups.all
      .map((tg) => tg.tabs)
      .flat()
      .filter(
        (tab) =>
          tab.input instanceof vscode.TabInputText &&
          arePathsEqual(tab.input.uri.fsPath, FileSystemHelper.getRemotePath(absolutePath))
      );
    for (const tab of tabs) {
      if (!tab.isDirty) {
        await vscode.window.tabGroups.close(tab);
      }
      this.documentWasOpen = true;
    }
    // 在静默模式下，跳过打开diff编辑器
    if (this.isSilentMode) {
      // 静默模式：不需要创建编辑器，直接处理内容
      this.activeDiffEditor = undefined;
    } else {
      try {
        this.activeDiffEditor = await this.openDiffEditor();
      } catch (error) {
        console.error(`Error opening diff editor: ${error}`);
        throw new Error(`打开diff窗口异常: ${error}`);
      }
    }

    if (!this.isSilentMode && this.activeDiffEditor) {
      this.fadedOverlayController = new DecorationController('fadedOverlay', this.activeDiffEditor);
      this.activeLineController = new DecorationController('activeLine', this.activeDiffEditor);
      // Apply faded overlay to all lines initially
      this.fadedOverlayController.addLines(0, this.activeDiffEditor.document.lineCount);
      // For new files, scroll to line 0; for existing files, we'll scroll to first diff after content is updated
      if (this.editType === 'create') {
        this.scrollEditorToLine(0);
      }
    }
    this.streamedLines = [];
  }

  async update(accumulatedContent: string, isFinal: boolean) {
    if (!this.relPath) {
      throw new Error(JoyCoderEditorMessageMap['Required values not set']);
    }

    // 检查是否启用静默模式 - 从GlobalState获取，默认开启
    const isSilentMode = GlobalState.get('isSilentMode');
    this.isSilentMode = isSilentMode === undefined ? true : isSilentMode;

    // 在静默模式下，跳过装饰器检查和后续所有diff相关操作
    if (this.isSilentMode) {
      // 只更新内容，不做装饰、滚动、diff等操作
      this.newContent = accumulatedContent;
      this.streamedLines = accumulatedContent.split('\n');
      return;
    }
    if (!this.activeLineController || !this.fadedOverlayController) {
      throw new Error(JoyCoderEditorMessageMap['Required values not set']);
    }
    // --- Fix to prevent duplicate BOM ---
    // Strip potential BOM from incoming content. VS Code's `applyEdit` might implicitly handle the BOM
    // when replacing from the start (0,0), and we want to avoid duplication.
    // Final BOM is handled in `saveChanges`.
    if (accumulatedContent.startsWith('\ufeff')) {
      accumulatedContent = accumulatedContent.slice(1); // Remove the BOM character
    }
    this.newContent = accumulatedContent;
    const accumulatedLines = accumulatedContent.split('\n');
    if (!isFinal) {
      accumulatedLines.pop(); // remove the last partial line only if it's not the final update
    }
    const diffEditor = this.activeDiffEditor;
    const document = diffEditor?.document;
    if (!diffEditor || !document) {
      throw new Error(JoyCoderEditorMessageMap['User closed text editor, unable to edit file...']);
    }

    // Place cursor at the beginning of the diff editor to keep it out of the way of the stream animation
    const beginningOfDocument = new vscode.Position(0, 0);
    diffEditor.selection = new vscode.Selection(beginningOfDocument, beginningOfDocument);

    const endLine = accumulatedLines.length;
    // Replace all content up to the current line with accumulated lines
    // This is necessary (as compared to inserting one line at a time) to handle cases where html tags on previous lines are auto closed for example
    const edit = new vscode.WorkspaceEdit();
    const rangeToReplace = new vscode.Range(0, 0, endLine, 0);
    const contentToReplace = accumulatedLines.slice(0, endLine + 1).join('\n') + '\n';
    edit.replace(document.uri, rangeToReplace, contentToReplace);
    await vscode.workspace.applyEdit(edit);

    // 在非静默模式下更新装饰器
    if (!this.isSilentMode) {
      // Update decorations
      this.activeLineController!.setActiveLine(endLine);
      this.fadedOverlayController!.updateOverlayAfterLine(endLine, document.lineCount);

      // For existing files, scroll to first diff on the first update
      if (!this.hasScrolledToFirstDiff && this.editType === 'modify' && this.streamedLines.length === 0) {
        // Use setTimeout to ensure the content is fully applied before scrolling
        setTimeout(() => {
          this.scrollToFirstDiff();
        }, 100);
        this.hasScrolledToFirstDiff = true;
      } else {
        // Scroll to the current line for streaming updates
        const ranges = this.activeDiffEditor?.visibleRanges;
        if (ranges && ranges.length > 0 && ranges[0].start.line < endLine && ranges[0].end.line > endLine) {
          this.scrollEditorToLine(endLine);
        }
      }
    }

    // Update the streamedLines with the new accumulated content
    this.streamedLines = accumulatedLines;
    if (isFinal) {
      // Handle any remaining lines if the new content is shorter than the original
      if (this.streamedLines.length < document.lineCount) {
        const edit = new vscode.WorkspaceEdit();
        edit.delete(document.uri, new vscode.Range(this.streamedLines.length, 0, document.lineCount, 0));
        await vscode.workspace.applyEdit(edit);
      }
      // Add empty last line if original content had one
      const hasEmptyLastLine = this.originalContent?.endsWith('\n');
      if (hasEmptyLastLine) {
        const accumulatedLines = accumulatedContent.split('\n');
        if (accumulatedLines[accumulatedLines.length - 1] !== '') {
          accumulatedContent += '\n';
        }
      }
      // Apply the final content.
      const finalEdit = new vscode.WorkspaceEdit();

      finalEdit.replace(
        document.uri,
        new vscode.Range(0, 0, document.lineCount, 0),
        this.stripAllBOMs(accumulatedContent)
      );

      await vscode.workspace.applyEdit(finalEdit);
      // Clear all decorations at the end (before applying final edit) - 仅在非静默模式下
      if (!this.isSilentMode) {
        this.fadedOverlayController!.clear();
        this.activeLineController!.clear();
      }
    }
  }

  async saveChanges(): Promise<{
    newProblemsMessage: string | undefined;
    userEdits: string | undefined;
    autoFormattingEdits: string | undefined;
    finalContent: string | undefined;
  }> {
    if (!this.relPath || !this.newContent) {
      return {
        newProblemsMessage: undefined,
        userEdits: undefined,
        autoFormattingEdits: undefined,
        finalContent: undefined,
      };
    }

    const absolutePath = FileSystemHelper.resolveUri(this.cwd, this.relPath);

    // 检查是否启用静默模式 - 从GlobalState获取，默认开启
    const isSilentMode = GlobalState.get('isSilentMode');
    this.isSilentMode = isSilentMode === undefined ? true : isSilentMode;

    // 静默模式下的保存逻辑
    if (this.isSilentMode) {
      return await this.saveSilently(absolutePath);
    }

    // 非静默模式需要activeDiffEditor
    if (!this.activeDiffEditor) {
      return {
        newProblemsMessage: undefined,
        userEdits: undefined,
        autoFormattingEdits: undefined,
        finalContent: undefined,
      };
    }

    const updatedDocument = this.activeDiffEditor.document;

    // get the contents before save operation which may do auto-formatting
    const preSaveContent = updatedDocument.getText();

    // 特殊处理 memory.md 文件，直接使用本地文件系统保存
    // if (isMemoryFile(this.relPath)) {
    //   await files.ensureDir(path.dirname(this.relPath));
    //   await files.writeFile(this.relPath, preSaveContent);
    // } else {
    if (updatedDocument.isDirty) {
      await updatedDocument.save();
    }
    // }
    // get text after save in case there is any auto-formatting done by the editor
    // 在静默模式下，不显示文档，非静默模式下也不再自动打开文档
    await this.closeAllDiffViews();

    // 获取保存后的内容
    let postSaveContent: string = '';
    // if (isMemoryFile(this.relPath)) {
    //   // 对于 memory.md 文件，从本地文件系统读取
    //   postSaveContent = await files.readFile(this.relPath, 'utf-8');
    // } else {
    postSaveContent = updatedDocument.getText();
    // }
    const postDiagnostics = vscode.languages.getDiagnostics();
    const newProblems = diagnosticsToProblemsString(
      getNewDiagnostics(this.preDiagnostics, postDiagnostics),
      [
        vscode.DiagnosticSeverity.Error, // only including errors since warnings can be distracting (if user wants to fix warnings they can use the @problems mention)
      ],
      FileSystemHelper.getRemotePath(this.cwd)
    ); // will be empty string if no errors
    const newProblemsMessage =
      newProblems.length > 0 ? `\n\nNew problems detected after saving the file:\n${newProblems}` : '';

    // If the edited content has different EOL characters, we don't want to show a diff with all the EOL differences.
    const newContentEOL = this.newContent.includes('\r\n') ? '\r\n' : '\n';
    const normalizedPreSaveContent = preSaveContent.replace(/\r\n|\n/g, newContentEOL).trimEnd() + newContentEOL; // trimEnd to fix issue where editor adds in extra new line automatically
    const normalizedPostSaveContent = postSaveContent.replace(/\r\n|\n/g, newContentEOL).trimEnd() + newContentEOL; // this is the final content we return to the model to use as the new baseline for future edits
    // just in case the new content has a mix of varying EOL characters
    const normalizedNewContent = this.newContent.replace(/\r\n|\n/g, newContentEOL).trimEnd() + newContentEOL;

    let userEdits: string | undefined = undefined;
    if (normalizedPreSaveContent !== normalizedNewContent) {
      // user made changes before approving edit. let the model know about user made changes (not including post-save auto-formatting changes)
      userEdits = formatResponse.createPrettyPatch(
        this.relPath.toPosix(),
        normalizedNewContent,
        normalizedPreSaveContent
      );
      // return { newProblemsMessage, userEdits, finalContent: normalizedPostSaveContent }
    } else {
      // no changes to joycoder's edits
      // return { newProblemsMessage, userEdits: undefined, finalContent: normalizedPostSaveContent }
    }

    let autoFormattingEdits: string | undefined = undefined;
    if (normalizedPreSaveContent !== normalizedPostSaveContent) {
      // auto-formatting was done by the editor
      autoFormattingEdits = formatResponse.createPrettyPatch(
        FileSystemHelper.getRemotePath(this.relPath).toPosix(),
        normalizedPreSaveContent,
        normalizedPostSaveContent
      );
    }

    return {
      newProblemsMessage,
      userEdits,
      autoFormattingEdits,
      finalContent: normalizedPostSaveContent,
    };
  }

  async revertChanges(): Promise<void> {
    if (!this.relPath) {
      return;
    }

    // 检查是否启用静默模式 - 从GlobalState获取，默认开启
    const isSilentMode = GlobalState.get('isSilentMode');
    this.isSilentMode = isSilentMode === undefined ? true : isSilentMode;

    // 静默模式下的回滚逻辑
    if (this.isSilentMode) {
      await this.revertSilently();
      return;
    }

    // 非静默模式需要activeDiffEditor
    if (!this.activeDiffEditor) {
      return;
    }
    const fileExists = this.editType === 'modify';
    const updatedDocument = this.activeDiffEditor.document;
    const absolutePath = FileSystemHelper.resolve(this.cwd, this.relPath);
    if (!(await FileSystemHelper.exists(absolutePath))) {
      await this.reset();
      return;
    }
    if (!fileExists) {
      if (updatedDocument.isDirty) {
        await updatedDocument.save();
      }
      await this.closeAllDiffViews();
      const absePath = FileSystemHelper.resolveUri(this.cwd, this.relPath);
      await FileSystemHelper.unlink(absePath);
      // Remove only the directories we created, in reverse order
      for (let i = this.createdDirs.length - 1; i >= 0; i--) {
        await FileSystemHelper.rmdir(this.createdDirs[i]);
        console.log(`目录 ${this.createdDirs[i]} 已被删除。`);
      }
      console.log(`文件 ${absolutePath} 已被删除。`);
    } else {
      // revert document
      const edit = new vscode.WorkspaceEdit();
      const fullRange = new vscode.Range(
        updatedDocument.positionAt(0),
        updatedDocument.positionAt(updatedDocument.getText().length)
      );
      edit.replace(updatedDocument.uri, fullRange, this.originalContent ?? '');
      // Apply the edit and save, since contents shouldnt have changed this wont show in local history unless of course the user made changes and saved during the edit
      await vscode.workspace.applyEdit(edit);
      await updatedDocument.save();
      console.log(`文件 ${absolutePath} 已被恢复到其原始内容。`);
      if (this.documentWasOpen) {
        const uri = FileSystemHelper.getUri(FileSystemHelper.resolveUri(this.cwd, this.relPath));
        console.log('diff###', isRemoteEnvironment(), uri);
        await vscode.window.showTextDocument(uri, {
          preview: false,
        });
      }
      await this.closeAllDiffViews();
    }

    // edit is done
    await this.reset();
  }

  private async closeAllDiffViews() {
    const tabs = vscode.window.tabGroups.all
      .flatMap((tg) => tg.tabs)
      .filter(
        (tab) => tab.input instanceof vscode.TabInputTextDiff && tab.input?.original?.scheme === DIFF_VIEW_URI_SCHEME
      );
    for (const tab of tabs) {
      // trying to close dirty views results in save popup
      if (!tab.isDirty) {
        await vscode.window.tabGroups.close(tab);
      }
    }
  }

  private async openDiffEditor(): Promise<vscode.TextEditor> {
    if (!this.relPath) {
      throw new Error(JoyCoderEditorMessageMap['No file path set']);
    }

    // 获取文件扩展名
    const fileExt = path.extname(this.relPath).toLowerCase();

    // 对于SVG文件，使用直接替换而不打开差异编辑器
    if (fileExt === '.svg') {
      try {
        // 创建必要的目录
        if (this.editType === 'create') {
          const dirPath = path.dirname(FileSystemHelper.resolve(this.cwd, this.relPath));
          await FileSystemHelper.mkdir(dirPath, { recursive: true });
        }

        // 直接写入文件内容
        const absolutePath = FileSystemHelper.resolveUri(this.cwd, this.relPath);
        await FileSystemHelper.writeFile(absolutePath, this.newContent || '');

        // 创建一个临时编辑器以满足返回类型要求
        const uri = FileSystemHelper.getUri(FileSystemHelper.resolveUri(this.cwd, this.relPath));
        const document = await vscode.workspace.openTextDocument(uri);
        return await vscode.window.showTextDocument(document);
      } catch (error) {
        console.error('处理SVG文件时出错:', error);
        throw new Error(`处理SVG文件时出错: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // 原有的差异编辑器逻辑
    let uri: vscode.Uri;
    if (this.cwd instanceof vscode.Uri) {
      uri = vscode.Uri.joinPath(this.cwd, this.relPath);
    } else {
      // 在远程环境中，需要正确处理 URI
      const resolvedPath = FileSystemHelper.resolveUri(this.cwd, this.relPath);
      uri = FileSystemHelper.getUri(resolvedPath);
    }

    // 检查是否已有相同的差异编辑器打开
    const diffTab = vscode.window.tabGroups.all
      .flatMap((group) => group.tabs)
      .find(
        (tab) =>
          tab.input instanceof vscode.TabInputTextDiff &&
          tab.input?.original?.scheme === DIFF_VIEW_URI_SCHEME &&
          arePathsEqual(tab.input.modified.fsPath, FileSystemHelper.getRemotePath(uri))
      );
    if (diffTab && diffTab.input instanceof vscode.TabInputTextDiff) {
      const editor = await vscode.window.showTextDocument(diffTab.input.modified);
      return editor;
    }

    // Open new diff editor
    return new Promise<vscode.TextEditor>((resolve, reject) => {
      const fileName = FileSystemHelper.basename(uri);
      const fileExists = this.editType === 'modify';
      const disposable = vscode.window.onDidChangeActiveTextEditor((editor) => {
        try {
          if (
            editor &&
            arePathsEqual(FileSystemHelper.getRemotePath(editor.document.uri), FileSystemHelper.getRemotePath(uri))
          ) {
            disposable.dispose();
            resolve(editor);
          }
        } catch (error) {
          console.error(
            '%c [ openDiffEditor-onDidChangeActiveTextEditor-eror ]-399',
            'font-size:13px; background:pink; color:#bf2c9f;',
            error
          );
        }
      });
      try {
        vscode.commands.executeCommand(
          'vscode.diff',
          vscode.Uri.parse(`${DIFF_VIEW_URI_SCHEME}:${fileName}`).with({
            query: Buffer.from(this.originalContent ?? '').toString('base64'),
          }),
          uri,
          `${fileName}: ${fileExists ? '原文件 ↔ JoyCode修改的文件' : '新建文件'} (编辑)`,
          // `${fileName}: ${fileExists ? "Original ↔ JoyCoder's Changes" : 'New File'} (Editable)`,
          {
            preserveFocus: true,
          }
        );
      } catch (error) {
        console.error("%c [ 执行 'vscode.diff'异常 ]-412", 'font-size:13px; background:pink; color:#bf2c9f;', error);
      }
      // This may happen on very slow machines ie project idx
      setTimeout(() => {
        disposable.dispose();
        reject(new Error(JoyCoderEditorMessageMap['Failed to open diff editor, please try again...']));
      }, 10_000);
    });
  }

  private scrollEditorToLine(line: number) {
    // 检查是否启用静默模式 - 从GlobalState获取，默认开启
    const isSilentMode = GlobalState.get('isSilentMode');
    this.isSilentMode = isSilentMode === undefined ? true : isSilentMode;

    // 在静默模式下跳过滚动操作
    if (this.isSilentMode || !this.activeDiffEditor) {
      return;
    }
    const scrollLine = line + 4;
    this.activeDiffEditor.revealRange(
      new vscode.Range(scrollLine, 0, scrollLine, 0),
      vscode.TextEditorRevealType.InCenter
    );
  }

  scrollToFirstDiff() {
    // 检查是否启用静默模式 - 从GlobalState获取，默认开启
    const isSilentMode = GlobalState.get('isSilentMode');
    this.isSilentMode = isSilentMode === undefined ? true : isSilentMode;

    // 在静默模式下跳过滚动操作
    if (this.isSilentMode || !this.activeDiffEditor) {
      return;
    }
    const currentContent = this.activeDiffEditor.document.getText();
    const diffs = diff.diffLines(this.originalContent || '', currentContent);
    let lineCount = 0;
    for (const part of diffs) {
      if (part.added || part.removed) {
        // Found the first diff, scroll to it
        this.activeDiffEditor.revealRange(
          new vscode.Range(lineCount, 0, lineCount, 0),
          vscode.TextEditorRevealType.InCenter
        );
        return;
      }
      if (!part.removed) {
        lineCount += part.count || 0;
      }
    }
  }

  private stripAllBOMs(input: string): string {
    let result = input;
    let previous: string = '';

    do {
      previous = result;
      result = stripBom(result);
    } while (result !== previous);

    return result;
  }

  // close editor if open?
  async reset() {
    await this.closeAllDiffViews();
    this.editType = undefined;
    this.isEditing = false;
    this.originalContent = undefined;
    this.createdDirs = [];
    this.documentWasOpen = false;
    this.activeDiffEditor = undefined;
    this.fadedOverlayController = undefined;
    this.activeLineController = undefined;
    this.streamedLines = [];
    this.preDiagnostics = [];
    this.hasScrolledToFirstDiff = false;
  }
}
