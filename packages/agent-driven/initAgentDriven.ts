import delay from 'delay';
import * as vscode from 'vscode';
import { DIFF_VIEW_URI_SCHEME } from './src/integrations/editor/DiffViewProvider';
import { createJoyCoderAPI } from './src/exports';
import { JoyCoderProvider } from './src/core/webview/JoycoderProvider';
import './src/utils/path'; // necessary to have access to String.prototype.toPosix
let agentDisposable: vscode.Disposable = new vscode.Disposable(() => {});
let tabDisposable: vscode.Disposable = new vscode.Disposable(() => {});
import {
  getVscodeConfig,
  isIDE,
  setVscodeConfig,
  queryRemoteConfigAsync,
  setGlobalConfig,
  clearLoginCookie,
} from '@joycoder/shared';
import { getJoyCoderContextMenus } from '@joycoder/plugin-base-ai/src/contextMenu';
import { updateLoginStatus } from '@joycoder/plugin-base-ai/src/dialog';
import { mcprocessDistribution } from './src/services/mcp/McpProcessDistribution';

// import { CodeIndexManager } from './src/services/code-index/manager';
// import { startQdrantServer } from './src/services/code-index/server/qdrant';

export { createJoyCoderAPI, JoyCoderProvider };

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export default async function (context: vscode.ExtensionContext) {
  const sidebarProvider = new JoyCoderProvider(context);
  agentDisposable && agentDisposable.dispose(); // Dispose of any previous instance
  context.subscriptions.push(
    (agentDisposable = vscode.window.registerWebviewViewProvider(JoyCoderProvider.sideBarId, sidebarProvider, {
      webviewOptions: { retainContextWhenHidden: true },
    }))
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('JoyCode.Coder.Logout', async () => {
      await sidebarProvider.postMessageToWebview({ type: 'updateLoginStatus', data: { isLogin: false } });
    })
  );
  context.subscriptions.push(
    vscode.commands.registerCommand('joycoder.Coder.plusButtonClicked', async () => {
      // 完全清除任务栈，确保创建全新的任务而不是恢复父任务
      // 与startNewTask的逻辑保持一致
      await sidebarProvider.clearTask();
      await sidebarProvider.removeJoyCoderFromStack();

      await sidebarProvider.postStateToWebview();
      await sidebarProvider.postMessageToWebview({
        type: 'action',
        action: 'chatButtonClicked',
      });
    })
  );
  context.subscriptions.push(
    vscode.commands.registerCommand('joycoder.Coder.mcpButtonClicked', () => {
      // 跳转到MCP设置页面的配置标签
      vscode.commands.executeCommand('JoyCode.config.setting', { type: 'mcp', mcpSubtype: 'installed' });
    })
  );
  context.subscriptions.push(
    vscode.commands.registerCommand('joycoder.autoCode.syncState', () => synchronizationStateToWebview(sidebarProvider))
  );
  context.subscriptions.push(
    vscode.commands.registerCommand('joycode.autoCode.mcp.msg', (data) => synchronizationMcpMsg(sidebarProvider, data))
  );
  context.subscriptions.push(
    // 切换为Chat
    vscode.commands.registerCommand('joycoder.switch.chat', async () => {
      try {
        await vscode.workspace.getConfiguration().update('JoyCode.switch.AgentView', false, false);
      } catch (e) {
        setVscodeConfig('JoyCode.switch.AgentView', false);
      }
      vscode.commands.executeCommand('JoyCode-left-view.focus');
    }),
    // 切换为Coder
    vscode.commands.registerCommand('joycoder.switch.Coder', async () => {
      try {
        await vscode.workspace.getConfiguration().update('JoyCode.switch.AgentView', true, false);
      } catch (e) {
        setVscodeConfig('JoyCode.switch.AgentView', true);
      }
      vscode.commands.executeCommand('joycoder.joycoder.SidebarProvider.focus');
    })
  );
  // 让每次开启新窗口时，joycoder都激活
  const focusJoyCoderSidebar = () => {
    if (getVscodeConfig('JoyCode.config.autoActivateOnNewWindow')) {
      vscode.commands.executeCommand('joycoder.joycoder.SidebarProvider.focus');
      vscode.commands.executeCommand('JoyCode-left-view.focus');
    }
    // }
  };
  // 如果插件激活时已有窗口打开，也执行一次
  focusJoyCoderSidebar();
  if (isIDE()) {
    context.subscriptions.push(
      vscode.commands.registerCommand('joycoder.joycoder.openInNewChat', () => {
        sidebarProvider.postMessageToWebview({
          type: 'action',
          action: 'openInNewChat',
        });
      })
    );
    context.subscriptions.push(
      vscode.commands.registerCommand('joycoder.joycoder.showAuxiliaryBar', () => {
        setAuxiliaryBar();
      })
    );
    // 如果是IDE环境，则注册命令
    // 注册命令，允许外部调用JoyCoder功能
    context.subscriptions.push(
      vscode.commands.registerCommand(
        'joycoder.joycoderEditor.initModes',
        async (params: { mode?: string; text?: string }) => {
          // 先检测并加载mode.json文件(自定义模式配置文件)
          const customModes = await sidebarProvider.customModesManager?.checkAndLoadRoomodes();

          if (customModes) {
            console.log(`[initModes] 成功加载${customModes.length}个自定义模式`);

            // 如果指定了模式且该模式存在于自定义模式中，则切换到该模式
            if (params.mode && customModes.some((mode) => mode.agentId === params.mode)) {
              console.log(`[initModes] 切换到指定模式: ${params.mode}`);
              await sidebarProvider.handleModeSwitch(params.mode);
            }
          }

          if (!sidebarProvider.getCurrentJoyCoder()) {
            // 如果JoyCoder实例不存在，先初始化
            await sidebarProvider.clearTask();
            await sidebarProvider.postStateToWebview();
            await sidebarProvider.initJoyCoderWithTask(params.text);
          }

          // 调用Provider的方法处理命令
          await sidebarProvider.handleExternalCommand(params);
        }
      )
    );

    // 注册命令，应用生成功能
    context.subscriptions.push(
      vscode.commands.registerCommand(
        'joycode.joycoderEditor.applicationGeneration',
        async (params: { mode?: string; text?: string }) => {
          // 设置模式为 'resource'
          params.mode = 'resource';

          // 先显示确认弹框，而不是直接执行初始化
          await sidebarProvider.postMessageToWebview({
            type: 'showApplicationGenerationDialog',
            applicationGenerationParams: params,
          });
        }
      )
    );

    // 注册命令，资源市场调用
    context.subscriptions.push(
      vscode.commands.registerCommand('joycode.Code.ResourceImport', (resourceFileName: { fileName: string }) => {
        sidebarProvider.postMessageToWebview({
          type: 'ResourceImport',
          data: {
            fileName: resourceFileName.fileName,
          },
        });
      })
    );
    setAuxiliaryBar();

    vscode.commands.executeCommand('workbench.action.joycoderIsLoggedIn', 'joycode.login.callback');
  }
  const openJoyCoderInNewTab = async () => {
    // (this example uses webviewProvider activation event which is necessary to deserialize cached webview, but since we use retainContextWhenHidden, we don't need to use that event)
    // https://github.com/microsoft/vscode-extension-samples/blob/main/webview-sample/src/extension.ts
    const tabProvider = new JoyCoderProvider(context);
    //const column = vscode.window.activeTextEditor ? vscode.window.activeTextEditor.viewColumn : undefined
    const lastCol = Math.max(...vscode.window.visibleTextEditors.map((editor) => editor.viewColumn || 0));
    const hasSmileTab = vscode.window.tabGroups.all.filter((tab) => tab.activeTab?.label === 'Coder');
    // Check if there are any visible text editors, otherwise open a new group to the right
    const hasVisibleEditors = vscode.window.visibleTextEditors.length > 0;
    if (!hasVisibleEditors && hasSmileTab.length === 0) {
      await vscode.commands.executeCommand('workbench.action.newGroupRight');
    }

    const targetCol = hasVisibleEditors ? Math.max(lastCol + 1, 1) : vscode.ViewColumn.Two;
    const panel = vscode.window.createWebviewPanel(
      JoyCoderProvider.tabPanelId,
      'Coder',
      { viewColumn: targetCol, preserveFocus: true },
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [context.extensionUri],
      }
    );
    // TODO: use better svg icon with light and dark variants (see https://stackoverflow.com/questions/58365687/vscode-extension-iconpath)

    panel.iconPath = {
      light: vscode.Uri.joinPath(context.extensionUri, 'assets', 'icons', 'robot_panel_light.png'),
      dark: vscode.Uri.joinPath(context.extensionUri, 'assets', 'icons', 'robot_panel_dark.png'),
    };
    tabProvider.resolveWebviewView(panel);

    // Lock the editor group so clicking on files doesn't open them over the panel
    await delay(2900);
    await vscode.commands.executeCommand('workbench.action.lockEditorGroup');
  };

  // 创建编辑区窗口
  tabDisposable && tabDisposable.dispose(); // Dispose of any previous instance
  context.subscriptions.push(
    (tabDisposable = vscode.commands.registerCommand('joycoder.joycoder.openInNewTab', openJoyCoderInNewTab))
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('joycoder.joycoder.historyButtonClicked', () => {
      sidebarProvider.refreshTotalTasksSize();
      sidebarProvider.postMessageToWebview({
        type: 'action',
        action: 'historyButtonClicked',
      });
    })
  );

  context.subscriptions.push(
    vscode.commands.registerCommand('joycode.login.callback', (params) => {
      if (!params.isLoggedIn) {
        clearLoginCookie();
        updateLoginStatus(false);
      }

      recursiveLoginInfo(sidebarProvider, params);
    })
  );

  /*
	We use the text document content provider API to show the left side for diff view by creating a virtual document for the original content. This makes it readonly so users know to edit the right side if they want to keep their changes.

	- This API allows you to create readonly documents in VSCode from arbitrary sources, and works by claiming an uri-scheme for which your provider then returns text contents. The scheme must be provided when registering a provider and cannot change afterwards.
	- Note how the provider doesn't create uris for virtual documents - its role is to provide contents given such an uri. In return, content providers are wired into the open document logic so that providers are always considered.
	https://code.visualstudio.com/api/extension-guides/virtual-documents
	*/
  const diffContentProvider = new (class implements vscode.TextDocumentContentProvider {
    provideTextDocumentContent(uri: vscode.Uri): string {
      return Buffer.from(uri.query, 'base64').toString('utf-8');
    }
  })();
  context.subscriptions.push(
    vscode.workspace.registerTextDocumentContentProvider(DIFF_VIEW_URI_SCHEME, diffContentProvider)
  );

  // URI Handler
  const handleUri = async (uri: vscode.Uri) => {
    const path = uri.path;
    const query = new URLSearchParams(uri.query.replace(/\+/g, '%2B'));
    const visibleProvider = JoyCoderProvider.getVisibleInstance();
    if (!visibleProvider) {
      return;
    }
    switch (path) {
      case '/auth': {
        const token = query.get('token');
        const state = query.get('state');

        console.log('Auth callback received:', {
          token: token,
          state: state,
        });
        break;
      }

      default:
        break;
    }
  };
  try {
    const uriHandler = vscode.window.registerUriHandler({ handleUri } as vscode.UriHandler);
    context.subscriptions.push(uriHandler);
  } catch (error) {
    console.warn(error.message);
  }

  getJoyCoderContextMenus(context, sidebarProvider);

  return createJoyCoderAPI(sidebarProvider);
}

function setAuxiliaryBar() {
  const isAuxiliaryBarVisible = vscode.workspace.getConfiguration('workbench').get('auxiliaryBar.visible');
  if (!isAuxiliaryBarVisible) {
    vscode.commands.executeCommand('workbench.action.focusAuxiliaryBar');
  }
}
async function synchronizationStateToWebview(sidebarProvider: JoyCoderProvider) {
  // 调用传入的sidebarProvider对象的postStateToWebview方法，将状态同步到webview
  // 该函数作为一个包装器，简化了状态同步操作，直接返回postStateToWebview方法的执行结果
  const { autoApprovalSettings } = await sidebarProvider.getStateToPostToWebview();
  sidebarProvider?.getCurrentJoyCoder()?.updateAutoApprovalSettings(autoApprovalSettings);
  return sidebarProvider.postStateToWebview();
}
async function synchronizationMcpMsg(sidebarProvider: JoyCoderProvider, data: any) {
  return mcprocessDistribution(sidebarProvider, data);
}
/**
 * 递归检查登录状态并更新侧边栏
 * @param sidebarProvider - 侧边栏提供者
 */
async function recursiveLoginInfo(
  sidebarProvider: JoyCoderProvider,
  params?: {
    isLoggedIn?: boolean;
    userInfo?: {
      tenant?: string;
      masterBaseUrl?: string;
      pt_key?: string;
    };
  }
) {
  if (!isIDE()) return;
  const { isLoggedIn, userInfo = {} } = params || {};
  const joyCoderBaseUrl = userInfo?.masterBaseUrl;
  const tenant = userInfo?.tenant;

  let jdhLoginInfo = {};

  if (isLoggedIn) {
    jdhLoginInfo = {
      ...userInfo,
      ptKey: userInfo?.pt_key,
    };
  }

  await sidebarProvider.updateGlobalState('jdhLoginInfo', jdhLoginInfo);

  // 重新设置全局配置信息
  // await sidebarProvider.updateGlobalState('joyCoderEnv', joyCoderEnv);
  await sidebarProvider.updateGlobalState('joyCoderBaseUrl', joyCoderBaseUrl);

  // 更新登录态
  updateLoginStatus(!!isLoggedIn);
  // sidebarProvider.postMessageToWebview({ type: 'updateLoginType', data: { joyCoderEnv } });
  sidebarProvider.postStateToWebview();

  sidebarProvider.postMessageToWebview({ type: 'updateLoginStatus', data: { isLogin: isLoggedIn } });

  // 更新设置登录态
  // settingWebviewPostMessage('login-user-info', userInfo);
  // codebase是否显示
  await vscode.workspace.getConfiguration().update('JoyCode.enableCodebase', tenant === 'JD', true);

  // 内外网切换更新大模型列表
  if (isLoggedIn) {
    const baseUrl = vscode.workspace.getConfiguration().get('server.baseUrl');
    setGlobalConfig({
      // 识别多租户
      // joyCoderBaseUrl,

      // baseUrl覆盖接口返回的baseUrl
      joyCoderBaseUrl:
        (vscode.workspace.getConfiguration().get('server.baseUrl') as string | undefined) !== ''
          ? (baseUrl as string | undefined)
          : joyCoderBaseUrl,
    });
    await queryRemoteConfigAsync();
    sidebarProvider.getModelList();
  }
}

export function deactivateAgent() {
  // 清理所有注册的命令和事件监听器
  agentDisposable && agentDisposable.dispose();
  tabDisposable && tabDisposable.dispose();
}
