// 库
import * as vscode from 'vscode';
import * as fs from 'fs-extra';
import * as path from 'path';
import to from 'await-to-js';
import { serializeError } from 'serialize-error';
// 业务库
import { Logger, getSelectedText, getTempFolderPath, openInBrowser } from '@joycoder/shared';
import { needUpdateVsCodeGuide } from '@joycoder/version';
import WebviewPanelManager from './webview-panel/WebviewPanelManager';
import { DEFAULT_START_URL } from './shared/constant';
// 定义
import type BrowserPage from './browser/BrowserPage';

/**
 * 内置H5开发调试模块
 */
export default function initBaseBrowser(context: vscode.ExtensionContext) {
  // 生成全局唯一 webview 窗口管理类对象
  const manager = new WebviewPanelManager(context);
  // 追加注册相关命令
  context.subscriptions.push(
    // 注册浏览器打开命令
    vscode.commands.registerCommand(
      'JoyCode.browser.open',
      async (url, callback?: (browserPage: BrowserPage) => void) => {
        // vscode.getEditorLayout命令最低版本需1.77
        if (needUpdateVsCodeGuide('1.78.0')) return;

        // 支持打开光标选中的链接
        const selectedText = getSelectedText() || '';
        const selUrl = selectedText.match(/https?:\/\/[-A-Za-z0-9+&@#\/%?=~_|!:,.;]+[-A-Za-z0-9+&@#\/%=~_|]/gi)?.[0];
        const finalUrl = url || selUrl || DEFAULT_START_URL;

        vscode.window.withProgress(
          {
            cancellable: false,
            title: 'JoyCode: 正在启动内置浏览器(Playwright)...',
            location: vscode.ProgressLocation.Notification,
          },
          async () => {
            try {
              // 创建新的 vscode webview面板
              const { browserPage } = await manager.create(finalUrl);
              // 如果存在回调则执行，便于处理一些特殊逻辑
              callback && callback(browserPage);
            } catch (error) {
              console.error('打开内置浏览器异常', error);
              // const btn = '用默认浏览器打开';
              // const [, result] = await to(
              //   Logger.showErrorMessage(
              //     `打开内置浏览器功能异常~ 错误信息：${JSON.stringify(serializeError(error))}`,
              //     btn
              //   )
              // );
              // if (result == btn) {
              //   openInBrowser(finalUrl);
              // }
            }
            return Promise.resolve();
          }
        );
      }
    ),
    // 注册文件选择栏右击预览调试命令
    vscode.commands.registerCommand('JoyCode.browser.preview.explorer', async (fileUri: vscode.Uri) => {
      // @ts-ignore
      openInVscodeWithBrowser(fileUri._formatted as string);
    }),
    // 注册编辑区右击预览调试命令
    vscode.commands.registerCommand('JoyCode.browser.preview', async (fileUri: vscode.Uri) => {
      // @ts-ignore
      openInVscodeWithBrowser(fileUri._formatted as string);
    }),
    // 注册浏览器清除缓存数据命令
    vscode.commands.registerCommand('JoyCode.browser.clearData', async () => {
      try {
        const userDataDir = path.join(context.globalStorageUri.fsPath, 'playwrightUserData');
        fs.removeSync(userDataDir);
      } catch (error) {
        console.error('清除异常', error);
      }
    })
  );
}

/**
 * 在vscode中使用playwright打开内置浏览器
 * @param url
 */
export function openInVscodeWithBrowser(url: string, callback?: (browserPage: BrowserPage) => void) {
  vscode.commands.executeCommand('JoyCode.browser.open', url, callback);
}
/**
 * 根据代码创建文件并在vscode中使用playwright打开内置浏览器
 * @param url
 */
export async function openHtmlCodeInVscodeWithBrowser(code: string) {
  const htmlFile = path.join(getTempFolderPath(), './htmlPreview/test.html');
  if (!fs.existsSync(htmlFile)) {
    fs.mkdirSync(path.dirname(htmlFile), { recursive: true });
  }
  fs.writeFileSync(htmlFile, code);

  const uri = vscode.Uri.file(htmlFile);
  await vscode.window.showTextDocument(uri);

  // @ts-ignore
  openInVscodeWithBrowser(uri._formatted as string);
}
