const path = require('path');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const webpack = require('webpack');
// const CopyPlugin = require('copy-webpack-plugin');
const { merge } = require('webpack-merge');
const commonConfig = require('./webpack.common');
const { isProduction } = require('./scripts/build/utils');

let projectRoot = process.cwd();
if (projectRoot.includes('vscode-IDE')) {
  projectRoot = path.resolve(projectRoot, '../..');
}
//@ts-check
/** @typedef {import('webpack').Configuration} WebpackConfig **/

const _CWDDIR = process.cwd();
/** @type WebpackConfig */
let extensionConfig = {
  target: 'node',
  entry: './src/extension.ts',
  output: {
    path: path.resolve(_CWDDIR, 'dist'),
    filename: 'extension.js',
    libraryTarget: 'commonjs2',
  },
  externals: {
    vscode: 'commonjs vscode',
    // 无用文件扫描
    vue: 'var {}',
    // 无用代码扫描
    // typescript无法external，vscode内部无法require到全局安装的包，可通过：require.resolve.paths('typescript') 查看
    // typescript: 'typescript',
    // HTML转SCSS
    canvas: 'var {}',
    bufferutil: 'var {}',
    'utf-8-validate': 'var {}',
    'playwright-core': 'commonjs ./playwright-core',
    'pg-cloudflare': 'commonjs pg-cloudflare',
    'cloudflare:sockets': 'commonjs cloudflare:sockets',
  },
  resolve: {
    extensions: ['.ts', '.js'],
    fallback: {
      'cloudflare:sockets': false,
    },
  },
  plugins: [
    new CleanWebpackPlugin({
      verbose: true,
      cleanOnceBeforeBuildPatterns: [path.resolve(_CWDDIR, 'dist/*.*')],
    }),
    new webpack.IgnorePlugin({
      resourceRegExp: /^cloudflare:sockets$/,
    }),
    // 修复 ws 库引入的bug，https://github.com/cyrus-and/chrome-remote-interface/issues/501
    new webpack.EnvironmentPlugin({
      WS_NO_BUFFER_UTIL: 1,
      WS_NO_UTF_8_VALIDATE: 1,
    }),
    // new webpack.DefinePlugin({
    //   'process.env': {
    //     PLUGIN_VER: JSON.stringify(process.env.PLUGIN_VER),
    //     BUSINESS_ENV: JSON.stringify(process.env.BUSINESS_ENV),
    //   },
    // }),
    new webpack.DefinePlugin({
      'process.env.PLUGIN_VER': JSON.stringify(process.env.PLUGIN_VER),
      'process.env.BUSINESS_ENV': JSON.stringify(process.env.BUSINESS_ENV),
    }),
    new CopyPlugin({
      patterns: [
        {
          from: 'node_modules/playwright-core',
          to: 'playwright-core',
        },
        {
          from: path.resolve(projectRoot, 'packages/agent-driven/node_modules/tiktoken/lite/tiktoken_bg.wasm'),
          to: 'tiktoken_bg.wasm',
        },
        {
          from: path.resolve(projectRoot, 'packages/agent-driven/node_modules/tiktoken/lite/tiktoken_bg.wasm'),
          to: 'workers/tiktoken_bg.wasm',
        },
      ],
    }),
  ],
};

if (!isProduction()) {
  // vscode调试需要
  extensionConfig.devtool = 'nosources-source-map';
}

module.exports = merge(commonConfig, extensionConfig);
